-- Globale Konfiguration für das Spiel
-- Wird sowohl vom Client als auch vom Server verwendet

local Config = {}

-- Spiel-Einstellungen
Config.Game = {
    Name = "Multifunktionales Roblox Spiel",
    Version = "1.0.0",
    MaxPlayersPerServer = 12,
    MaxServerGroups = 20,
    TargetAgeRange = {Min = 6, Max = 60}
}

-- Spieler-Einstellungen
Config.Player = {
    DefaultCurrency = 100,
    MaxInventorySlots = 50,
    DefaultHealth = 100,
    DefaultWalkSpeed = 16,
    DefaultJumpPower = 50
}

-- Datenbank-Einstellungen
Config.DataStore = {
    PlayerDataKey = "PlayerData_v1",
    GlobalDataKey = "GlobalData_v1",
    AutoSaveInterval = 60, -- Sekunden
    RetryAttempts = 3
}

-- UI-Einstellungen
Config.UI = {
    DefaultTweenTime = 0.3,
    DefaultEasingStyle = Enum.EasingStyle.Quad,
    DefaultEasingDirection = Enum.EasingDirection.Out,
    MobileSupport = true
}

-- Economy-Einstellungen
Config.Economy = {
    DefaultCurrency = "Coins",
    StartingAmount = 100,
    DailyBonus = 50,
    MaxCurrency = *********
}

-- Debug-Einstellungen
Config.Debug = {
    Enabled = true,
    LogLevel = "INFO", -- DEBUG, INFO, WARN, ERROR
    ShowPlayerJoinMessages = true,
    ShowDataSaveMessages = true
}

return Config
