# Rojo Plugin Diagnose und Installation

Write-Host "Rojo Plugin Diagnose wird gestartet..." -ForegroundColor Green

# Prüfe Plugin-Pfade
$pluginPaths = @(
    "$env:LOCALAPPDATA\Roblox\Plugins",
    "$env:APPDATA\Roblox\Plugins",
    "$env:USERPROFILE\AppData\Local\Roblox\Plugins"
)

Write-Host "📁 Prüfe Plugin-Verzeichnisse:" -ForegroundColor Yellow
foreach ($path in $pluginPaths) {
    if (Test-Path $path) {
        Write-Host "✅ Gefunden: $path" -ForegroundColor Green
        $plugins = Get-ChildItem $path -Filter "*.rbxm" -ErrorAction SilentlyContinue
        if ($plugins) {
            Write-Host "   Plugins:" -ForegroundColor Cyan
            foreach ($plugin in $plugins) {
                Write-Host "   - $($plugin.Name)" -ForegroundColor White
            }
        }
        else {
            Write-Host "   Keine .rbxm Plugins gefunden" -ForegroundColor Gray
        }
    }
    else {
        Write-Host "❌ Nicht gefunden: $path" -ForegroundColor Red
    }
}

# Kopiere Plugin in alle möglichen Pfade
Write-Host "`n🔧 Installiere Plugin in alle Pfade..." -ForegroundColor Yellow
foreach ($path in $pluginPaths) {
    try {
        if (!(Test-Path $path)) {
            New-Item -ItemType Directory -Path $path -Force | Out-Null
        }
        Copy-Item "C:\RobloxTools\Rojo.rbxm" -Destination "$path\Rojo.rbxm" -Force
        Write-Host "✅ Plugin kopiert nach: $path" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Fehler beim Kopieren nach: $path" -ForegroundColor Red
    }
}

# Prüfe Roblox Studio Installation
Write-Host "`n🎮 Prüfe Roblox Studio Installation..." -ForegroundColor Yellow
$robloxPaths = @(
    "$env:LOCALAPPDATA\Roblox\Versions",
    "$env:PROGRAMFILES\Roblox\Versions",
    "${env:PROGRAMFILES(X86)}\Roblox\Versions"
)

foreach ($path in $robloxPaths) {
    if (Test-Path $path) {
        Write-Host "✅ Roblox gefunden: $path" -ForegroundColor Green
        $versions = Get-ChildItem $path -Directory -ErrorAction SilentlyContinue
        if ($versions) {
            $latest = $versions | Sort-Object Name -Descending | Select-Object -First 1
            Write-Host "   Neueste Version: $($latest.Name)" -ForegroundColor Cyan
        }
        break
    }
}

Write-Host "`n📋 NÄCHSTE SCHRITTE:" -ForegroundColor Cyan
Write-Host "1. Starte Roblox Studio KOMPLETT neu (alle Fenster schließen)" -ForegroundColor White
Write-Host "2. Öffne ein neues Place in Studio" -ForegroundColor White
Write-Host "3. Suche in der Toolbar nach dem Rojo-Icon" -ForegroundColor White
Write-Host "4. Falls nicht sichtbar: Plugins → Manage Plugins → Rojo aktivieren" -ForegroundColor White
Write-Host "5. Alternative: Installiere über Marketplace (Browser ist geöffnet)" -ForegroundColor White

Write-Host "`n🌐 MARKETPLACE-INSTALLATION:" -ForegroundColor Cyan
Write-Host "- Gehe zu: https://create.roblox.com/marketplace/asset/13916111004/Rojo" -ForegroundColor White
Write-Host "- Klicke 'Get' um das Plugin zu installieren" -ForegroundColor White
Write-Host "- Starte Studio neu" -ForegroundColor White

Write-Host ""
Write-Host "Plugin-Installation abgeschlossen!" -ForegroundColor Green
