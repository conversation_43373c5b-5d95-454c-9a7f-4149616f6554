{"lua.workspace.library": ["C:/Users/<USER>/AppData/Local/Roblox/Versions/*/RobloxStudioBeta.exe"], "lua.diagnostics.globals": ["game", "workspace", "script", "plugin", "shared"], "lua.runtime.version": "<PERSON><PERSON>", "lua.workspace.checkThirdParty": false, "files.associations": {"*.lua": "lua", "*.luau": "lua", "*.project.json": "json", "*.model.json": "json", "*.meta.json": "json"}, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/*.rbxl": true, "**/*.rbxlx": true, "**/*.rbxm": true, "**/*.rbxmx": true}, "search.exclude": {"**/node_modules": true, "**/*.rbxl": true, "**/*.rbxlx": true, "**/*.rbxm": true, "**/*.rbxmx": true}, "git.ignoreLimitWarning": true, "terminal.integrated.defaultProfile.windows": "PowerShell", "rojo.enableRichPresence": true}