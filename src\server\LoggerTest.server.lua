-- Logger-System Test
-- Testet alle Logger-Funktionen automatisch

print("🧪 Logger-System Test wird gestartet...")

-- Services laden
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Warte auf Module
local SharedModules = ReplicatedStorage:WaitForChild("Shared")
local Logger = require(SharedModules.Logger)
local Config = require(SharedModules.Config)

-- Test-Funktion
local function runLoggerTests()
    print("=" .. string.rep("=", 50))
    print("🔍 LOGGER-SYSTEM TESTS")
    print("=" .. string.rep("=", 50))
    
    -- Test 1: Basis-Logging
    Logger.debug("🔧 Debug-Test: System-Initialisierung")
    Logger.info("ℹ️ Info-Test: Logger-System funktioniert")
    Logger.warn("⚠️ Warn-Test: Dies ist eine Test-Warnung")
    Logger.error("❌ Error-Test: Dies ist ein Test-Fehler")
    
    -- Test 2: S<PERSON><PERSON>lle <PERSON>gger-Funktionen
    Logger.playerAction({Name = "TestSpieler"}, "Login", "Erfolgreich angemeldet")
    Logger.systemEvent("TestSystem", "Initialisiert", "Alle Module geladen")
    
    -- Test 3: Formatierte Nachrichten
    Logger.info("Spiel-Name: %s", Config.Game.Name)
    Logger.info("Max Spieler: %d", Config.Game.MaxPlayersPerServer)
    Logger.info("Währung: %s (Start: %d)", Config.Economy.DefaultCurrency, Config.Economy.StartingAmount)
    
    -- Test 4: Performance-Test
    local startTime = tick()
    for i = 1, 100 do
        Logger.debug("Performance-Test Nachricht #%d", i)
    end
    local endTime = tick()
    Logger.info("Performance-Test: 100 Nachrichten in %.3f Sekunden", endTime - startTime)
    
    print("=" .. string.rep("=", 50))
    print("✅ LOGGER-SYSTEM TESTS ABGESCHLOSSEN")
    print("=" .. string.rep("=", 50))
end

-- Tests nach kurzer Verzögerung ausführen
wait(1)
runLoggerTests()

-- Kontinuierlicher Test alle 30 Sekunden
spawn(function()
    while true do
        wait(30)
        Logger.info("🔄 Kontinuierlicher Logger-Test - System läuft seit %d Sekunden", tick())
    end
end)
