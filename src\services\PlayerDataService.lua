-- Player Data Service
-- Verwaltet persistente Spielerdaten über Server hinweg

local PlayerDataService = {}

-- Services
local Players = game:GetService("Players")
local DataStoreService = game:GetService("DataStoreService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Module laden
local Config = require(ReplicatedStorage.Shared.Config)
local Logger = require(ReplicatedStorage.Shared.Logger)

-- DataStore
local PlayerDataStore = DataStoreService:GetDataStore(Config.DataStore.PlayerDataKey)

-- Aktive Spielerdaten
local PlayerData = {}

-- Standard-Spielerdaten
local function getDefaultPlayerData()
    return {
        -- Basis-Informationen
        UserId = 0,
        Username = "",
        JoinDate = os.time(),
        LastPlayed = os.time(),
        TotalPlayTime = 0,
        
        -- Fortschritt
        Level = 1,
        Experience = 0,
        
        -- Währung
        Currency = {
            Coins = Config.Economy.StartingAmount,
            Gems = 0,
            Tokens = 0
        },
        
        -- Inventar
        Inventory = {
            Items = {},
            Equipment = {
                Weapon = nil,
                Armor = nil,
                Accessory = nil
            },
            MaxSlots = Config.Player.MaxInventorySlots
        },
        
        -- Statistiken
        Stats = {
            GamesPlayed = 0,
            Wins = 0,
            Losses = 0,
            KillCount = 0,
            DeathCount = 0,
            ItemsCrafted = 0,
            QuestsCompleted = 0
        },
        
        -- Achievements
        Achievements = {},
        
        -- Einstellungen
        Settings = {
            MusicVolume = 0.5,
            SFXVolume = 0.8,
            UIScale = 1.0,
            AutoSave = true
        },
        
        -- Freischaltungen
        Unlocks = {
            GameModes = {"Basic"},
            Items = {},
            Features = {}
        }
    }
end

-- Spielerdaten laden
function PlayerDataService.LoadPlayerData(player)
    local success, data = pcall(function()
        return PlayerDataStore:GetAsync(player.UserId)
    end)
    
    if success and data then
        Logger.info("Spielerdaten geladen für: %s", player.Name)
        PlayerData[player.UserId] = data
        PlayerData[player.UserId].LastPlayed = os.time()
    else
        Logger.info("Neue Spielerdaten erstellt für: %s", player.Name)
        PlayerData[player.UserId] = getDefaultPlayerData()
        PlayerData[player.UserId].UserId = player.UserId
        PlayerData[player.UserId].Username = player.Name
    end
    
    return PlayerData[player.UserId]
end

-- Spielerdaten speichern
function PlayerDataService.SavePlayerData(player)
    if not PlayerData[player.UserId] then
        Logger.warn("Keine Daten zum Speichern für Spieler: %s", player.Name)
        return false
    end
    
    local success, errorMessage = pcall(function()
        PlayerDataStore:SetAsync(player.UserId, PlayerData[player.UserId])
    end)
    
    if success then
        Logger.info("Spielerdaten gespeichert für: %s", player.Name)
        return true
    else
        Logger.error("Fehler beim Speichern für %s: %s", player.Name, errorMessage)
        return false
    end
end

-- Spielerdaten abrufen
function PlayerDataService.GetPlayerData(player)
    return PlayerData[player.UserId]
end

-- Währung hinzufügen
function PlayerDataService.AddCurrency(player, currencyType, amount)
    local data = PlayerData[player.UserId]
    if data and data.Currency[currencyType] then
        data.Currency[currencyType] = data.Currency[currencyType] + amount
        Logger.playerAction(player, "Currency Added", currencyType .. ": +" .. amount)
        return true
    end
    return false
end

-- Spielerdaten beim Verlassen löschen
function PlayerDataService.CleanupPlayerData(player)
    PlayerData[player.UserId] = nil
    Logger.info("Spielerdaten aus Speicher entfernt für: %s", player.Name)
end

return PlayerDataService
