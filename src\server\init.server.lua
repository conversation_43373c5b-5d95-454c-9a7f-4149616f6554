-- Server Hauptskript
-- Initialisiert alle Server-seitigen Services und Module

print("🚀 Server wird initialisiert...")

-- Services laden
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local DataStoreService = game:GetService("DataStoreService")
local HttpService = game:GetService("HttpService")

-- Shared Module laden
local SharedModules = ReplicatedStorage:WaitForChild("Shared")
local Modules = ReplicatedStorage:WaitForChild("Modules")

-- Services laden
local PlayerDataService = require(script.Parent.Services.PlayerDataService)
local InventoryService = require(script.Parent.Services.InventoryService)

-- Server Services
local ServerServices = {
    PlayerData = PlayerDataService,
    Inventory = InventoryService
}

-- Events und Functions erstellen
local Events = ReplicatedStorage:WaitForChild("Events")
local Functions = ReplicatedStorage:WaitForChild("Functions")

-- Hauptinitialisierung
local function initialize()
    print("✅ Server erfolgreich initialisiert")
    print("📊 Bereit für bis zu 12+ Spieler pro Server")
    print("🔗 ROJO LIVE-SYNCHRONISATION AKTIV!")
    print("🎮 Entwicklungsumgebung bereit für " .. SharedModules.Config.Game.Name)

    -- Services initialisieren
    for serviceName, service in pairs(ServerServices) do
        if service.Initialize then
            service.Initialize()
            print("🔧 " .. serviceName .. " Service initialisiert")
        end
    end

    print("🎯 Alle Services bereit!")
end

-- Player-Events
Players.PlayerAdded:Connect(function(player)
    print("👤 Spieler beigetreten:", player.Name)

    -- Spielerdaten laden
    ServerServices.PlayerData.LoadPlayerData(player)

    -- Inventar laden
    ServerServices.Inventory.LoadPlayerInventory(player)

    print("✅ " .. player.Name .. " vollständig initialisiert")
end)

Players.PlayerRemoving:Connect(function(player)
    print("👋 Spieler verlässt das Spiel:", player.Name)

    -- Spielerdaten speichern
    ServerServices.PlayerData.SavePlayerData(player)

    -- Cleanup
    ServerServices.PlayerData.CleanupPlayerData(player)
    ServerServices.Inventory.CleanupPlayer(player)

    print("💾 " .. player.Name .. " Daten gespeichert und bereinigt")
end)

-- Initialisierung starten
initialize()
