-- Inventory Service
-- Verwal<PERSON>t Spieler-Inventare, Items und Equipment

local InventoryService = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Module laden
local Config = require(ReplicatedStorage.Shared.Config)
local Logger = require(ReplicatedStorage.Shared.Logger)
local EventBus = require(ReplicatedStorage.Modules.EventBus)

-- Nur auf Server ausführen
if not RunService:IsServer() then
    return InventoryService
end

-- Private Variablen
local PlayerInventories = {}
local ItemDatabase = {}

-- Item-Typen
local ItemTypes = {
    WEAPON = "Weapon",
    ARMOR = "Armor",
    CONSUMABLE = "Consumable",
    MATERIAL = "Material",
    QUEST = "Quest",
    CURRENCY = "Currency"
}

-- Item-Seltenheit
local ItemRarity = {
    COMMON = {Name = "Common", Color = Color3.fromRGB(255, 255, 255), Multiplier = 1},
    UNCOMMON = {Name = "Uncommon", Color = Color3.fromRGB(0, 255, 0), Multiplier = 1.2},
    RARE = {Name = "Rare", Color = Color3.fromRGB(0, 100, 255), Multiplier = 1.5},
    EPIC = {Name = "Epic", Color = Color3.fromRGB(150, 0, 255), Multiplier = 2},
    LEGENDARY = {Name = "Legendary", Color = Color3.fromRGB(255, 165, 0), Multiplier = 3}
}

-- Standard-Inventar erstellen
local function createDefaultInventory()
    return {
        Items = {},
        Equipment = {
            Weapon = nil,
            Armor = nil,
            Accessory = nil
        },
        MaxSlots = Config.Player.MaxInventorySlots,
        UsedSlots = 0
    }
end

-- Item-Datenbank initialisieren
local function initializeItemDatabase()
    ItemDatabase = {
        -- Waffen
        ["sword_basic"] = {
            Id = "sword_basic",
            Name = "Basis Schwert",
            Type = ItemTypes.WEAPON,
            Rarity = ItemRarity.COMMON,
            Description = "Ein einfaches Schwert für Anfänger",
            Stats = {Damage = 10, Speed = 1.0},
            Price = 100,
            Stackable = false,
            MaxStack = 1
        },
        ["sword_iron"] = {
            Id = "sword_iron",
            Name = "Eisenschwert",
            Type = ItemTypes.WEAPON,
            Rarity = ItemRarity.UNCOMMON,
            Description = "Ein robustes Eisenschwert",
            Stats = {Damage = 20, Speed = 0.9},
            Price = 250,
            Stackable = false,
            MaxStack = 1
        },
        
        -- Rüstung
        ["armor_leather"] = {
            Id = "armor_leather",
            Name = "Lederrüstung",
            Type = ItemTypes.ARMOR,
            Rarity = ItemRarity.COMMON,
            Description = "Leichte Lederrüstung",
            Stats = {Defense = 5, Speed = 0.95},
            Price = 150,
            Stackable = false,
            MaxStack = 1
        },
        
        -- Verbrauchsgegenstände
        ["potion_health"] = {
            Id = "potion_health",
            Name = "Heiltrank",
            Type = ItemTypes.CONSUMABLE,
            Rarity = ItemRarity.COMMON,
            Description = "Stellt 50 HP wieder her",
            Stats = {Healing = 50},
            Price = 25,
            Stackable = true,
            MaxStack = 99
        },
        
        -- Materialien
        ["wood"] = {
            Id = "wood",
            Name = "Holz",
            Type = ItemTypes.MATERIAL,
            Rarity = ItemRarity.COMMON,
            Description = "Grundmaterial für Crafting",
            Stats = {},
            Price = 5,
            Stackable = true,
            MaxStack = 999
        },
        ["iron_ore"] = {
            Id = "iron_ore",
            Name = "Eisenerz",
            Type = ItemTypes.MATERIAL,
            Rarity = ItemRarity.UNCOMMON,
            Description = "Wertvolles Erz für Schmiedearbeiten",
            Stats = {},
            Price = 15,
            Stackable = true,
            MaxStack = 999
        },

        -- Neue Items für Shop
        ["sword_steel"] = {
            Id = "sword_steel",
            Name = "Stahlschwert",
            Type = ItemTypes.WEAPON,
            Rarity = ItemRarity.RARE,
            Description = "Ein scharfes Stahlschwert",
            Stats = {Damage = 35, Speed = 0.85},
            Price = 500,
            Stackable = false,
            MaxStack = 1
        },
        ["armor_iron"] = {
            Id = "armor_iron",
            Name = "Eisenrüstung",
            Type = ItemTypes.ARMOR,
            Rarity = ItemRarity.UNCOMMON,
            Description = "Robuste Eisenrüstung",
            Stats = {Defense = 15, Speed = 0.9},
            Price = 400,
            Stackable = false,
            MaxStack = 1
        },
        ["potion_mana"] = {
            Id = "potion_mana",
            Name = "Manatrank",
            Type = ItemTypes.CONSUMABLE,
            Rarity = ItemRarity.COMMON,
            Description = "Stellt 30 Mana wieder her",
            Stats = {ManaRestore = 30},
            Price = 30,
            Stackable = true,
            MaxStack = 99
        },
        ["gold_ore"] = {
            Id = "gold_ore",
            Name = "Golderz",
            Type = ItemTypes.MATERIAL,
            Rarity = ItemRarity.RARE,
            Description = "Seltenes Golderz",
            Stats = {},
            Price = 50,
            Stackable = true,
            MaxStack = 999
        }
    }
    
    Logger.systemEvent("InventoryService", "ItemDatabase", string.format("%d Items geladen", 
        table.getn(ItemDatabase)))
end

-- Spieler-Inventar laden
function InventoryService.LoadPlayerInventory(player)
    if PlayerInventories[player.UserId] then
        return PlayerInventories[player.UserId]
    end
    
    -- Neues Inventar erstellen
    PlayerInventories[player.UserId] = createDefaultInventory()
    
    -- Starter-Items hinzufügen
    InventoryService.AddItem(player, "sword_basic", 1)
    InventoryService.AddItem(player, "potion_health", 5)
    InventoryService.AddItem(player, "wood", 10)
    
    Logger.playerAction(player, "InventoryLoaded", "Neues Inventar erstellt")
    return PlayerInventories[player.UserId]
end

-- Item hinzufügen
function InventoryService.AddItem(player, itemId, quantity)
    quantity = quantity or 1
    local inventory = PlayerInventories[player.UserId]
    local itemData = ItemDatabase[itemId]
    
    if not inventory or not itemData then
        Logger.warn("AddItem fehlgeschlagen: Inventar oder Item nicht gefunden")
        return false
    end
    
    -- Prüfe ob Item stackable ist
    if itemData.Stackable then
        -- Suche existierenden Stack
        for i, item in ipairs(inventory.Items) do
            if item.Id == itemId and item.Quantity < itemData.MaxStack then
                local addAmount = math.min(quantity, itemData.MaxStack - item.Quantity)
                item.Quantity = item.Quantity + addAmount
                quantity = quantity - addAmount
                
                if quantity <= 0 then
                    Logger.playerAction(player, "ItemAdded", 
                        string.format("%s x%d (gestackt)", itemData.Name, addAmount))
                    EventBus:FireClient("InventoryUpdated", player, inventory)
                    return true
                end
            end
        end
    end
    
    -- Neuen Stack erstellen
    while quantity > 0 and inventory.UsedSlots < inventory.MaxSlots do
        local addAmount = itemData.Stackable and math.min(quantity, itemData.MaxStack) or 1
        
        table.insert(inventory.Items, {
            Id = itemId,
            Quantity = addAmount,
            Data = itemData
        })
        
        inventory.UsedSlots = inventory.UsedSlots + 1
        quantity = quantity - addAmount
        
        Logger.playerAction(player, "ItemAdded", 
            string.format("%s x%d", itemData.Name, addAmount))
    end
    
    if quantity > 0 then
        Logger.warn("Inventar voll: %d %s konnten nicht hinzugefügt werden", quantity, itemData.Name)
    end
    
    EventBus:FireClient("InventoryUpdated", player, inventory)
    return quantity == 0
end

-- Item entfernen
function InventoryService.RemoveItem(player, itemId, quantity)
    quantity = quantity or 1
    local inventory = PlayerInventories[player.UserId]
    
    if not inventory then
        return false
    end
    
    local removed = 0
    for i = #inventory.Items, 1, -1 do
        local item = inventory.Items[i]
        if item.Id == itemId then
            local removeAmount = math.min(quantity - removed, item.Quantity)
            item.Quantity = item.Quantity - removeAmount
            removed = removed + removeAmount
            
            if item.Quantity <= 0 then
                table.remove(inventory.Items, i)
                inventory.UsedSlots = inventory.UsedSlots - 1
            end
            
            if removed >= quantity then
                break
            end
        end
    end
    
    if removed > 0 then
        Logger.playerAction(player, "ItemRemoved", 
            string.format("%s x%d", ItemDatabase[itemId].Name, removed))
        EventBus:FireClient("InventoryUpdated", player, inventory)
    end
    
    return removed
end

-- Item ausrüsten
function InventoryService.EquipItem(player, itemId)
    local inventory = PlayerInventories[player.UserId]
    local itemData = ItemDatabase[itemId]
    
    if not inventory or not itemData then
        return false
    end
    
    -- Prüfe ob Item ausrüstbar ist
    if itemData.Type ~= ItemTypes.WEAPON and itemData.Type ~= ItemTypes.ARMOR then
        Logger.warn("Item %s ist nicht ausrüstbar", itemData.Name)
        return false
    end
    
    -- Bestimme Equipment-Slot
    local slot = itemData.Type == ItemTypes.WEAPON and "Weapon" or "Armor"
    
    -- Entferne vorheriges Equipment
    if inventory.Equipment[slot] then
        InventoryService.AddItem(player, inventory.Equipment[slot], 1)
    end
    
    -- Rüste neues Item aus
    if InventoryService.RemoveItem(player, itemId, 1) > 0 then
        inventory.Equipment[slot] = itemId
        Logger.playerAction(player, "ItemEquipped", itemData.Name)
        EventBus:FireClient("EquipmentUpdated", player, inventory.Equipment)
        return true
    end
    
    return false
end

-- Inventar-Informationen abrufen
function InventoryService.GetInventory(player)
    return PlayerInventories[player.UserId]
end

-- Item-Datenbank abrufen
function InventoryService.GetItemData(itemId)
    return ItemDatabase[itemId]
end

-- Service initialisieren
function InventoryService.Initialize()
    initializeItemDatabase()
    
    -- Event-Handler
    EventBus:OnServerEvent("GetInventory", function(player)
        local inventory = InventoryService.GetInventory(player)
        EventBus:FireClient("InventoryData", player, inventory)
    end)
    
    EventBus:OnServerEvent("EquipItem", function(player, itemId)
        InventoryService.EquipItem(player, itemId)
    end)
    
    EventBus:OnServerEvent("UseItem", function(player, itemId)
        -- Hier später Item-Verwendung implementieren
        Logger.playerAction(player, "ItemUsed", itemId)
    end)
    
    Logger.systemEvent("InventoryService", "Initialized", "Service bereit")
end

-- Cleanup beim Verlassen
function InventoryService.CleanupPlayer(player)
    PlayerInventories[player.UserId] = nil
    Logger.playerAction(player, "InventoryCleanup", "Inventar aus Speicher entfernt")
end

return InventoryService
