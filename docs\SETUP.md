# 🛠️ Entwicklungsumgebung Setup

## Voraussetzungen

### Software installieren:

1. **Visual Studio Code**
   - Download: https://code.visualstudio.com/
   - Installiere die folgenden Extensions:
     - Roblox LSP
     - Lua Language Server
     - GitLens
     - Bracket Pair Colorizer

2. **Node.js** (für Rojo)
   - Download: https://nodejs.org/
   - Version 16+ empfohlen

3. **Git** (für Versionskontrolle)
   - Download: https://git-scm.com/
   - Konfiguriere deinen Namen und E-Mail

4. **Rojo** (Synchronisation)
   ```bash
   npm install -g @roblox/rojo
   ```

## VS Code Konfiguration

### Empfohlene Extensions:
```
- sumneko.lua (Lua Language Server)
- evaera.vscode-rojo (Rojo Extension)
- ms-vscode.vscode-json (JSON Support)
- streetsidesoftware.code-spell-checker (Rechtschreibprüfung)
```

### VS Code Settings (settings.json):
```json
{
    "lua.workspace.library": [
        "path/to/roblox/types"
    ],
    "files.associations": {
        "*.lua": "lua"
    },
    "editor.tabSize": 4,
    "editor.insertSpaces": true
}
```

## Rojo Setup

1. **Rojo installieren** (falls noch nicht geschehen):
   ```bash
   npm install -g @roblox/rojo
   ```

2. **Projekt starten**:
   ```bash
   cd c:\Users\<USER>\RobloxEntwicklung
   rojo serve
   ```

3. **In Roblox Studio**:
   - Installiere das Rojo Plugin
   - Klicke "Connect" im Plugin
   - Wähle "localhost:34872"

## Git Setup

1. **Repository initialisieren**:
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   ```

2. **GitHub Repository erstellen**:
   - Gehe zu GitHub.com
   - Erstelle neues Repository
   - Folge den Anweisungen zum Verknüpfen

3. **Empfohlene .gitignore**:
   ```
   # Roblox Studio Files
   *.rbxl
   *.rbxlx
   *.rbxm
   *.rbxmx
   
   # OS Files
   .DS_Store
   Thumbs.db
   
   # IDE Files
   .vscode/settings.json
   .idea/
   
   # Logs
   *.log
   ```

## Workflow

1. **Code in VS Code schreiben**
2. **Rojo läuft im Hintergrund** (automatische Synchronisation)
3. **In Roblox Studio testen**
4. **Änderungen committen**:
   ```bash
   git add .
   git commit -m "Beschreibung der Änderungen"
   git push
   ```

## Troubleshooting

### Rojo verbindet nicht:
- Überprüfe ob Port 34872 frei ist
- Starte Rojo neu: `rojo serve`
- Überprüfe Firewall-Einstellungen

### VS Code erkennt Lua nicht:
- Installiere Lua Language Server Extension
- Überprüfe file associations in settings.json

### Git Probleme:
- Überprüfe Git-Konfiguration: `git config --list`
- Setze Credentials: `git config user.name "Dein Name"`
