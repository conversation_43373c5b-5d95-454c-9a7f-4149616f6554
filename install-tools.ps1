# Automatisches Installationsskript fuer Roblox-Entwicklungstools
# <PERSON><PERSON><PERSON> dieses Skript als Administrator aus

Write-Host "Installiere Entwicklungstools fuer Roblox..." -ForegroundColor Green

# Pruefe ob Node.js installiert ist
try {
    $nodeVersion = & "C:\Program Files\nodejs\node.exe" --version
    Write-Host "Node.js ist bereits installiert: $nodeVersion" -ForegroundColor Green
}
catch {
    Write-Host "Node.js nicht gefunden" -ForegroundColor Red
    exit 1
}

# Erstelle Tools-Verzeichnis
$toolsDir = "C:\RobloxTools"
if (!(Test-Path $toolsDir)) {
    New-Item -ItemType Directory -Path $toolsDir -Force
    Write-Host "Tools-Verzeichnis erstellt: $toolsDir" -ForegroundColor Yellow
}

# Lade Rojo herunter
Write-Host "Lade Rojo herunter..." -ForegroundColor Yellow
$rojoUrl = "https://github.com/rojo-rbx/rojo/releases/download/v7.5.1/rojo-7.5.1-win64.zip"
$rojoZip = "$toolsDir\rojo.zip"

try {
    Invoke-WebRequest -Uri $rojoUrl -OutFile $rojoZip -UseBasicParsing
    Write-Host "Rojo heruntergeladen" -ForegroundColor Green
}
catch {
    Write-Host "Fehler beim Herunterladen von Rojo: $($_.Exception.Message)" -ForegroundColor Red
    # Versuche alternative URL
    $rojoUrl = "https://github.com/rojo-rbx/rojo/releases/download/v7.5.1/rojo-win64.zip"
    try {
        Invoke-WebRequest -Uri $rojoUrl -OutFile $rojoZip -UseBasicParsing
        Write-Host "Rojo mit alternativer URL heruntergeladen" -ForegroundColor Green
    }
    catch {
        Write-Host "Auch alternative URL fehlgeschlagen" -ForegroundColor Red
        Write-Host "Bitte lade Rojo manuell herunter von: https://github.com/rojo-rbx/rojo/releases/latest" -ForegroundColor Cyan
    }
}

# Entpacke Rojo (falls heruntergeladen)
if (Test-Path $rojoZip) {
    Write-Host "Entpacke Rojo..." -ForegroundColor Yellow
    Expand-Archive -Path $rojoZip -DestinationPath $toolsDir -Force
    Remove-Item $rojoZip
    Write-Host "Rojo entpackt" -ForegroundColor Green
}

# Fuege Tools zum PATH hinzu
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($currentPath -notlike "*$toolsDir*") {
    [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$toolsDir", "User")
    Write-Host "Tools zum PATH hinzugefuegt" -ForegroundColor Green
    Write-Host "Starte VS Code neu, damit der PATH aktualisiert wird" -ForegroundColor Yellow
}

# Erstelle Batch-Datei für einfachen Zugriff
$batchContent = @"
@echo off
echo Starte Rojo Server...
cd /d "C:\Users\<USER>\RobloxEntwicklung"
"$toolsDir\rojo.exe" serve
pause
"@

$batchFile = "$toolsDir\start-rojo.bat"
$batchContent | Out-File -FilePath $batchFile -Encoding ASCII
Write-Host "Rojo-Startskript erstellt: $batchFile" -ForegroundColor Green

Write-Host ""
Write-Host "Installation abgeschlossen!" -ForegroundColor Green
Write-Host ""
Write-Host "Naechste Schritte:" -ForegroundColor Cyan
Write-Host "1. Starte VS Code neu" -ForegroundColor White
Write-Host "2. Installiere VS Code Extensions:" -ForegroundColor White
Write-Host "   - Roblox LSP" -ForegroundColor Gray
Write-Host "   - Lua Language Server" -ForegroundColor Gray
Write-Host "3. Fuehre 'rojo serve' in deinem Projektordner aus" -ForegroundColor White
Write-Host "4. Oder verwende: $batchFile" -ForegroundColor White
Write-Host ""
Write-Host "Rojo Plugin fuer Studio: https://create.roblox.com/marketplace/asset/13916111004/Rojo" -ForegroundColor Cyan
