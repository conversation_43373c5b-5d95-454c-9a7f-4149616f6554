{"version": "2.0.0", "tasks": [{"label": "Rojo: Serve", "type": "shell", "command": "rojo", "args": ["serve"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "runOptions": {"instanceLimit": 1}, "detail": "Startet den Rojo-Server für Live-Synchronisation"}, {"label": "Rojo: Build", "type": "shell", "command": "rojo", "args": ["build", "-o", "game.rbxl"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Erstellt eine .rbxl-Datei aus dem Projekt"}, {"label": "Rojo: Install Plugin", "type": "shell", "command": "rojo", "args": ["plugin", "install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Installiert das Rojo-Plugin in Roblox Studio"}, {"label": "NPM: Install", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Installiert NPM-Abhängigkeiten"}]}