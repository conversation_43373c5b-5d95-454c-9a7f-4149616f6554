-- Logging-System für Debug und Monitoring
-- Unterstützt verschiedene Log-Level und formatierte Ausgaben

local Logger = {}

-- Log-Level
local LogLevels = {
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4
}

-- Aktuelle Konfiguration
local currentLogLevel = LogLevels.INFO
local enableTimestamp = true
local enableColors = true

-- Farben für verschiedene Log-Level (für Studio Console)
local LogColors = {
    DEBUG = "🔍",
    INFO = "ℹ️",
    WARN = "⚠️",
    ERROR = "❌"
}

-- Log-Level setzen
function Logger.setLogLevel(level)
    if LogLevels[level] then
        currentLogLevel = LogLevels[level]
    end
end

-- Timestamp generieren
local function getTimestamp()
    if not enableTimestamp then
        return ""
    end
    return "[" .. os.date("%H:%M:%S") .. "] "
end

-- Formatierte Log-Nachricht erstellen
local function formatMessage(level, message, ...)
    local timestamp = getTimestamp()
    local icon = LogColors[level] or ""
    local formattedMessage = string.format(message, ...)
    
    return timestamp .. icon .. " [" .. level .. "] " .. formattedMessage
end

-- Basis-Log-Funktion
local function log(level, message, ...)
    if LogLevels[level] >= currentLogLevel then
        local formattedMessage = formatMessage(level, message, ...)
        print(formattedMessage)
    end
end

-- Öffentliche Log-Funktionen
function Logger.debug(message, ...)
    log("DEBUG", message, ...)
end

function Logger.info(message, ...)
    log("INFO", message, ...)
end

function Logger.warn(message, ...)
    log("WARN", message, ...)
end

function Logger.error(message, ...)
    log("ERROR", message, ...)
end

-- Spezielle Funktionen
function Logger.playerAction(player, action, details)
    Logger.info("Spieler %s: %s - %s", player.Name, action, details or "")
end

function Logger.systemEvent(system, event, details)
    Logger.info("System [%s]: %s - %s", system, event, details or "")
end

return Logger
