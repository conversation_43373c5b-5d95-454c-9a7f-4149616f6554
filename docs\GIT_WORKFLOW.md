# 🌿 Git-Workflow - Professionelle Entwicklungsstrategie

## 📋 Branch-Strategie (Git Flow)

### **Haupt-Branches:**
- **`main`** - Produktions-ready Code, nur stabile Releases
- **`develop`** - Entwicklungs-Branch, Integration aller Features
- **`feature/*`** - Feature-Entwicklung (z.B. `feature/economy-system`)
- **`hotfix/*`** - Kritische Bugfixes für Production
- **`release/*`** - Release-Vorbereitung und Testing

### **Aktueller Branch-Status:**
```
main (stable)
├── develop (integration)
    ├── feature/economy-system (current)
    ├── feature/quest-system (planned)
    └── feature/combat-system (planned)
```

## 🔄 Entwicklungsworkflow

### **1. Neues Feature entwickeln:**
```bash
# Von develop ausgehend
git checkout develop
git pull origin develop
git checkout -b feature/feature-name

# Entwicklung...
git add .
git commit -m "feat: neue Funktionalität implementiert"

# Feature abschließen
git checkout develop
git merge feature/feature-name --no-ff
git branch -d feature/feature-name
git push origin develop
```

### **2. Hotfix erstellen:**
```bash
# Von main ausgehend
git checkout main
git checkout -b hotfix/critical-bug-fix

# Fix implementieren...
git add .
git commit -m "fix: kritischer Bug behoben"

# Hotfix mergen
git checkout main
git merge hotfix/critical-bug-fix --no-ff
git checkout develop
git merge hotfix/critical-bug-fix --no-ff
git branch -d hotfix/critical-bug-fix
```

### **3. Release erstellen:**
```bash
# Von develop ausgehend
git checkout develop
git checkout -b release/v1.0.0

# Release-Vorbereitung...
git add .
git commit -m "chore: Version 1.0.0 vorbereitet"

# Release abschließen
git checkout main
git merge release/v1.0.0 --no-ff
git tag -a v1.0.0 -m "Version 1.0.0"
git checkout develop
git merge release/v1.0.0 --no-ff
git branch -d release/v1.0.0
```

## 📝 Commit-Konventionen

### **Commit-Message Format:**
```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

### **Commit-Typen:**
- **`feat`** - Neue Funktionalität
- **`fix`** - Bugfix
- **`docs`** - Dokumentation
- **`style`** - Code-Formatierung
- **`refactor`** - Code-Refactoring
- **`test`** - Tests hinzufügen/ändern
- **`chore`** - Build-Prozess, Tools, etc.

### **Beispiele:**
```bash
feat(inventory): Item-Stacking implementiert
fix(player): Speicherfehler beim Logout behoben
docs(api): InventoryService Dokumentation erweitert
refactor(events): EventBus Performance optimiert
test(inventory): Unit-Tests für AddItem hinzugefügt
chore(build): Rojo-Konfiguration aktualisiert
```

## 🔀 Merge-Strategien

### **Feature → Develop:**
- **`--no-ff`** (No Fast-Forward) für klare Feature-Historie
- **Squash** bei vielen kleinen Commits
- **Code Review** vor Merge (bei Team-Entwicklung)

### **Develop → Main:**
- **Release-Branch** für Testing
- **Vollständige Tests** vor Merge
- **Tag** für Versionierung

### **Hotfix → Main + Develop:**
- **Sofortiger Merge** in beide Branches
- **Cherry-Pick** falls nötig

## 🏷️ Tagging-Strategie

### **Semantic Versioning:**
- **`v1.0.0`** - Major Release (Breaking Changes)
- **`v1.1.0`** - Minor Release (Neue Features)
- **`v1.1.1`** - Patch Release (Bugfixes)

### **Tag-Beispiele:**
```bash
git tag -a v0.1.0 -m "Alpha Release - Basis-Systeme"
git tag -a v0.2.0 -m "Beta Release - Inventory + Economy"
git tag -a v1.0.0 -m "Release - Vollständiges Spiel"
```

## 🚀 Automatisierung

### **Pre-Commit Hooks:**
- Code-Formatierung prüfen
- Tests ausführen
- Lint-Checks

### **CI/CD Pipeline (geplant):**
- Automatische Tests bei Push
- Build-Validierung
- Deployment bei Release

## 📊 Branch-Management

### **Aktuelle Branches:**
- `main` - Stabile Production-Version
- `develop` - Aktuelle Entwicklung
- `feature/economy-system` - Economy-System Entwicklung

### **Branch-Schutz:**
- `main` - Nur über Pull Requests
- `develop` - Code Review erforderlich
- Feature-Branches - Frei entwickelbar

## 🔧 Git-Konfiguration

### **Empfohlene Einstellungen:**
```bash
git config --global merge.ff false
git config --global pull.rebase true
git config --global push.default simple
git config --global core.autocrlf true
```

## 📈 Workflow-Metriken

### **Ziele:**
- **Commits pro Tag**: 3-5 (kleine, fokussierte Änderungen)
- **Feature-Dauer**: 1-3 Tage
- **Code Review**: Innerhalb 24h
- **Release-Zyklus**: Wöchentlich

### **Qualitätssicherung:**
- Jeder Commit muss funktionsfähig sein
- Tests müssen bestehen
- Dokumentation aktuell halten
- Code-Standards einhalten

---

**Dieser Workflow wird automatisch von der AI verwaltet und befolgt professionelle Entwicklungsstandards für skalierbare, wartbare Codebases.**
