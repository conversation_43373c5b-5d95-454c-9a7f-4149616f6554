-- Client Ha<PERSON>tskript
-- Initialisiert alle Client-seitigen Services und Module

print("🎮 Client wird initialisiert...")

-- Services laden
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Lokale Variablen
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Shared Module laden
local SharedModules = ReplicatedStorage:WaitForChild("Shared")
local Modules = ReplicatedStorage:WaitForChild("Modules")

-- Client Services initialisieren
local ClientServices = {}

-- Hauptinitialisierung
local function initialize()
    print("✅ Client erfolgreich initialisiert für Spieler:", player.Name)
    
    -- <PERSON>er werden später alle Client-Services gestartet
end

-- Initialisierung starten
initialize()
