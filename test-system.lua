-- Test-System für die Roblox-Entwicklungsumgebung
-- <PERSON>ühre dieses Skript in Roblox Studio aus, um zu testen ob alles funktioniert

print("🧪 Starte System-Tests...")

-- Test 1: Shared Module laden
local success1, Config = pcall(function()
    return require(game.ReplicatedStorage.Shared.Config)
end)

if success1 then
    print("✅ Test 1 BESTANDEN: Config-Modul geladen")
    print("   Spiel-Name:", Config.Game.Name)
    print("   Version:", Config.Game.Version)
else
    print("❌ Test 1 FEHLGESCHLAGEN: Config-Modul nicht gefunden")
end

-- Test 2: Logger-System testen
local success2, Logger = pcall(function()
    return require(game.ReplicatedStorage.Shared.Logger)
end)

if success2 then
    print("✅ Test 2 BESTANDEN: Logger-System geladen")
    Logger.info("Logger-Test erfolgreich!")
    Logger.warn("Dies ist eine Test-Warnung")
    Logger.debug("Debug-Nachricht (nur sichtbar wenn Debug aktiviert)")
else
    print("❌ Test 2 FEHLGESCHLAGEN: Logger-System nicht gefunden")
end

-- Test 3: EventBus-System testen
local success3, EventBus = pcall(function()
    return require(game.ReplicatedStorage.Modules.EventBus)
end)

if success3 then
    print("✅ Test 3 BESTANDEN: EventBus-System geladen")
    
    -- Test lokale Events
    local testReceived = false
    EventBus:Connect("TestEvent", function(message)
        testReceived = true
        print("   Event empfangen:", message)
    end)
    
    EventBus:Fire("TestEvent", "Hallo von EventBus!")
    
    wait(0.1) -- Kurz warten für Event-Verarbeitung
    
    if testReceived then
        print("✅ Test 3.1 BESTANDEN: Lokale Events funktionieren")
    else
        print("❌ Test 3.1 FEHLGESCHLAGEN: Lokale Events funktionieren nicht")
    end
else
    print("❌ Test 3 FEHLGESCHLAGEN: EventBus-System nicht gefunden")
end

-- Test 4: PlayerDataService testen (nur Server-seitig)
if game:GetService("RunService"):IsServer() then
    local success4, PlayerDataService = pcall(function()
        return require(game.ServerScriptService.Services.PlayerDataService)
    end)
    
    if success4 then
        print("✅ Test 4 BESTANDEN: PlayerDataService geladen (Server)")
    else
        print("❌ Test 4 FEHLGESCHLAGEN: PlayerDataService nicht gefunden")
    end
else
    print("⏭️ Test 4 ÜBERSPRUNGEN: PlayerDataService (nur Server-seitig)")
end

-- Test 5: Projektstruktur prüfen
local function checkPath(path, name)
    if path then
        print("✅ " .. name .. " gefunden")
        return true
    else
        print("❌ " .. name .. " nicht gefunden")
        return false
    end
end

print("\n📁 Projektstruktur-Test:")
local structureOK = true
structureOK = checkPath(game.ReplicatedStorage:FindFirstChild("Shared"), "Shared-Ordner") and structureOK
structureOK = checkPath(game.ReplicatedStorage:FindFirstChild("Modules"), "Modules-Ordner") and structureOK
structureOK = checkPath(game.ReplicatedStorage:FindFirstChild("Events"), "Events-Ordner") and structureOK
structureOK = checkPath(game.ReplicatedStorage:FindFirstChild("Functions"), "Functions-Ordner") and structureOK

if game:GetService("RunService"):IsServer() then
    structureOK = checkPath(game.ServerScriptService:FindFirstChild("Services"), "Services-Ordner") and structureOK
end

-- Zusammenfassung
print("\n📊 Test-Zusammenfassung:")
print("=" .. string.rep("=", 40))

local totalTests = 5
local passedTests = 0

if success1 then passedTests = passedTests + 1 end
if success2 then passedTests = passedTests + 1 end
if success3 then passedTests = passedTests + 1 end
if game:GetService("RunService"):IsServer() and success4 then passedTests = passedTests + 1 end
if structureOK then passedTests = passedTests + 1 end

print(string.format("Tests bestanden: %d/%d", passedTests, totalTests))

if passedTests == totalTests then
    print("🎉 ALLE TESTS BESTANDEN!")
    print("✅ Deine Roblox-Entwicklungsumgebung ist bereit!")
    print("📚 Nächste Schritte:")
    print("   1. Lies docs/DEVELOPMENT_GUIDE.md")
    print("   2. Beginne mit der Entwicklung deines Spiels")
    print("   3. Nutze das Logger-System für Debugging")
elseif passedTests >= totalTests * 0.8 then
    print("⚠️ MEISTE TESTS BESTANDEN")
    print("🔧 Kleine Probleme gefunden, aber grundsätzlich funktionsfähig")
else
    print("❌ MEHRERE TESTS FEHLGESCHLAGEN")
    print("🔧 Überprüfe die Installation und Rojo-Synchronisation")
    print("📖 Siehe INSTALLATION_ANLEITUNG.md für Hilfe")
end

print("=" .. string.rep("=", 40))
