# 🏗️ Spiel-Architektur

## Überblick

Das Spiel verwendet eine modulare, service-basierte Architektur mit klarer Trennung zwischen Client und Server.

## Architektur-Prinzipien

### 1. **Separation of Concerns**
- Client: UI, Input, Rendering
- Server: <PERSON><PERSON><PERSON>, Daten, Validierung
- Shared: Gemeinsame Utilities und Konfiguration

### 2. **Service-orientierte Architektur**
- Jeder Service hat eine spezifische Verantwortung
- Services kommunizieren über Events
- Lose Kopplung zwischen Komponenten

### 3. **Event-driven Communication**
- RemoteEvents für Client-Server Kommunikation
- BindableEvents für interne Kommunikation
- Typisierte Event-Parameter

## Ordnerstruktur

```
src/
├── client/              # Client-seitige Skripte
│   ├── init.client.lua  # Client-Hauptskript
│   ├── ui/              # UI-Controller
│   ├── input/           # Input-Handler
│   └── effects/         # Visuelle Effekte
│
├── server/              # Server-seitige Skripte
│   ├── init.server.lua  # Server-Hauptskript
│   ├── gameloop/        # Spiel-Loop Logic
│   └── validation/      # Input-Validierung
│
├── shared/              # Geteilte Module
│   ├── Config.lua       # Globale Konfiguration
│   ├── Logger.lua       # Logging-System
│   ├── Utils.lua        # Hilfsfunktionen
│   └── Constants.lua    # Konstanten
│
├── services/            # Game Services
│   ├── PlayerDataService.lua    # Spielerdaten
│   ├── InventoryService.lua     # Inventar-System
│   ├── EconomyService.lua       # Wirtschafts-System
│   ├── QuestService.lua         # Quest-System
│   └── CombatService.lua        # Kampf-System
│
├── modules/             # Wiederverwendbare Module
│   ├── ItemSystem.lua   # Item-Verwaltung
│   ├── StatSystem.lua   # Statistik-System
│   └── EventBus.lua     # Event-Management
│
├── components/          # UI-Komponenten
│   ├── MainMenu.lua     # Hauptmenü
│   ├── Inventory.lua    # Inventar-UI
│   └── Shop.lua         # Shop-UI
│
├── data/               # Datenstrukturen
│   ├── Items.lua       # Item-Definitionen
│   ├── Quests.lua      # Quest-Definitionen
│   └── GameModes.lua   # Spielmodus-Definitionen
│
└── utils/              # Hilfsfunktionen
    ├── MathUtils.lua   # Mathematische Funktionen
    ├── TableUtils.lua  # Tabellen-Utilities
    └── StringUtils.lua # String-Utilities
```

## Service-Architektur

### Core Services

1. **PlayerDataService**
   - Persistente Spielerdaten
   - Cross-Server Synchronisation
   - Auto-Save Funktionalität

2. **InventoryService**
   - Item-Management
   - Equipment-System
   - Trading-Mechaniken

3. **EconomyService**
   - Währungssystem
   - Shop-Funktionalität
   - Marktplatz

4. **QuestService**
   - Dynamische Aufgaben
   - Fortschritts-Tracking
   - Belohnungen

5. **CombatService**
   - Kampfmechaniken
   - Skill-System
   - PvP/PvE-Logic

### Service-Kommunikation

```lua
-- Event-basierte Kommunikation
local EventBus = require(ReplicatedStorage.Modules.EventBus)

-- Service A sendet Event
EventBus:Fire("PlayerLevelUp", player, newLevel)

-- Service B hört auf Event
EventBus:Connect("PlayerLevelUp", function(player, level)
    -- Reagiere auf Level-Up
end)
```

## Datenfluss

### Client → Server
1. Client sendet Input über RemoteEvent
2. Server validiert Input
3. Server aktualisiert Daten
4. Server sendet Update an alle Clients

### Server → Client
1. Server-Event tritt auf
2. Server sendet Update über RemoteEvent
3. Client aktualisiert UI
4. Client spielt Effekte ab

## Skalierbarkeit

### Multi-Server Support
- Persistente Daten in DataStore
- Cross-Server Messaging über MessagingService
- Server-übergreifende Freundeslisten

### Performance
- Lazy Loading von Modulen
- Event-Pooling für häufige Events
- Optimierte DataStore-Zugriffe

## Sicherheit

### Client-Server Validierung
- Alle Client-Inputs werden server-seitig validiert
- Rate-Limiting für RemoteEvents
- Anti-Cheat Mechanismen

### Daten-Integrität
- Verschlüsselte DataStore-Schlüssel
- Backup-Systeme für kritische Daten
- Rollback-Mechanismen bei Fehlern
