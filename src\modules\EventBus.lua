-- Event Bus System
-- Ermöglicht lose gekoppelte Kommunikation zwischen Services

local EventBus = {}

-- Private Variablen
local connections = {}
local remoteEvents = {}
local remoteFunctions = {}

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Prüfen ob wir auf Server oder Client sind
local isServer = RunService:IsServer()

-- Event-Verbindung erstellen
function EventBus:Connect(eventName, callback)
    if not connections[eventName] then
        connections[eventName] = {}
    end
    
    table.insert(connections[eventName], callback)
    
    -- Rückgabe-Funktion zum Trennen der Verbindung
    return function()
        for i, conn in ipairs(connections[eventName]) do
            if conn == callback then
                table.remove(connections[eventName], i)
                break
            end
        end
    end
end

-- Event feuern (lokal)
function EventBus:Fire(eventName, ...)
    if connections[eventName] then
        for _, callback in ipairs(connections[eventName]) do
            task.spawn(callback, ...)
        end
    end
end

-- Remote Event erstellen/abrufen
function EventBus:GetRemoteEvent(eventName)
    if not remoteEvents[eventName] then
        if isServer then
            -- Server erstellt RemoteEvent
            local remoteEvent = Instance.new("RemoteEvent")
            remoteEvent.Name = eventName
            remoteEvent.Parent = ReplicatedStorage.Events
            remoteEvents[eventName] = remoteEvent
        else
            -- Client wartet auf RemoteEvent
            remoteEvents[eventName] = ReplicatedStorage.Events:WaitForChild(eventName)
        end
    end
    
    return remoteEvents[eventName]
end

-- Remote Function erstellen/abrufen
function EventBus:GetRemoteFunction(functionName)
    if not remoteFunctions[functionName] then
        if isServer then
            -- Server erstellt RemoteFunction
            local remoteFunction = Instance.new("RemoteFunction")
            remoteFunction.Name = functionName
            remoteFunction.Parent = ReplicatedStorage.Functions
            remoteFunctions[functionName] = remoteFunction
        else
            -- Client wartet auf RemoteFunction
            remoteFunctions[functionName] = ReplicatedStorage.Functions:WaitForChild(functionName)
        end
    end
    
    return remoteFunctions[functionName]
end

-- Remote Event an Client(s) senden
function EventBus:FireClient(eventName, player, ...)
    if not isServer then
        warn("FireClient kann nur vom Server aufgerufen werden")
        return
    end
    
    local remoteEvent = self:GetRemoteEvent(eventName)
    remoteEvent:FireClient(player, ...)
end

-- Remote Event an alle Clients senden
function EventBus:FireAllClients(eventName, ...)
    if not isServer then
        warn("FireAllClients kann nur vom Server aufgerufen werden")
        return
    end
    
    local remoteEvent = self:GetRemoteEvent(eventName)
    remoteEvent:FireAllClients(...)
end

-- Remote Event an Server senden
function EventBus:FireServer(eventName, ...)
    if isServer then
        warn("FireServer kann nur vom Client aufgerufen werden")
        return
    end
    
    local remoteEvent = self:GetRemoteEvent(eventName)
    remoteEvent:FireServer(...)
end

-- Remote Function vom Client aufrufen
function EventBus:InvokeServer(functionName, ...)
    if isServer then
        warn("InvokeServer kann nur vom Client aufgerufen werden")
        return
    end
    
    local remoteFunction = self:GetRemoteFunction(functionName)
    return remoteFunction:InvokeServer(...)
end

-- Remote Function vom Server aufrufen
function EventBus:InvokeClient(functionName, player, ...)
    if not isServer then
        warn("InvokeClient kann nur vom Server aufgerufen werden")
        return
    end
    
    local remoteFunction = self:GetRemoteFunction(functionName)
    return remoteFunction:InvokeClient(player, ...)
end

-- Remote Event Listener hinzufügen
function EventBus:OnServerEvent(eventName, callback)
    if not isServer then
        warn("OnServerEvent kann nur vom Server aufgerufen werden")
        return
    end
    
    local remoteEvent = self:GetRemoteEvent(eventName)
    return remoteEvent.OnServerEvent:Connect(callback)
end

-- Client Event Listener hinzufügen
function EventBus:OnClientEvent(eventName, callback)
    if isServer then
        warn("OnClientEvent kann nur vom Client aufgerufen werden")
        return
    end
    
    local remoteEvent = self:GetRemoteEvent(eventName)
    return remoteEvent.OnClientEvent:Connect(callback)
end

return EventBus
