# 🛠️ Manuelle Installation - Roblox Entwicklungsumgebung

## ✅ Bereits erledigt:
- ✅ Node.js v24.1.0 installiert
- ✅ Projektstruktur erstellt
- ✅ Git Repository initialisiert
- ✅ VS Code Konfiguration erstellt
- ✅ Tools-Verzeichnis erstellt: `C:\RobloxTools`

## 📥 Noch zu installieren:

### 1. <PERSON><PERSON><PERSON> herunterladen
Da der automatische Download fehlgeschlagen ist, lade Rojo manuell herunter:

1. **Gehe zu**: https://github.com/rojo-rbx/rojo/releases/latest
2. **Lade herunter**: `rojo-7.5.1-win64.exe` (oder die neueste Windows-Version)
3. **Speichere die Datei als**: `C:\RobloxTools\rojo.exe`

### 2. Visual Studio Code Extensions installieren
Öffne VS Code und installiere diese Extensions:

```
- Lua Language Server (sumneko.lua)
- Rojo Extension (evaera.vscode-rojo)
- JSON Support (ms-vscode.vscode-json)
- Code Spell Checker (streetsidesoftware.code-spell-checker)
```

**Automatische Installation:**
1. Öffne VS Code in deinem Projektordner
2. Drücke `Ctrl+Shift+P`
3. Tippe: `Extensions: Show Recommended Extensions`
4. Installiere alle empfohlenen Extensions

### 3. Rojo Plugin für Roblox Studio
1. **Gehe zu**: https://create.roblox.com/marketplace/asset/13916111004/Rojo
2. **Klicke**: "Get" um das Plugin zu installieren
3. **Oder verwende**: `rojo plugin install` (nachdem Rojo installiert ist)

## 🚀 Projekt starten

### Methode 1: VS Code Tasks
1. Öffne VS Code in deinem Projektordner
2. Drücke `Ctrl+Shift+P`
3. Tippe: `Tasks: Run Task`
4. Wähle: `Rojo: Serve`

### Methode 2: Terminal
```bash
cd C:\Users\<USER>\RobloxEntwicklung
rojo serve
```

### Methode 3: Batch-Datei
Doppelklicke auf: `C:\RobloxTools\start-rojo.bat`

## 🔗 Roblox Studio verbinden

1. **Öffne Roblox Studio**
2. **Öffne das Rojo Plugin** (sollte in der Toolbar sein)
3. **Klicke "Connect"**
4. **Wähle**: `localhost:34872`
5. **Klicke "Sync"** um das Projekt zu laden

## ✅ Test der Installation

### 1. Rojo testen
```bash
# Öffne PowerShell/CMD
rojo --version
# Sollte ausgeben: rojo 7.5.1
```

### 2. Projekt testen
```bash
cd C:\Users\<USER>\RobloxEntwicklung
rojo serve
# Sollte ausgeben: Rojo server listening on port 34872
```

### 3. Studio-Verbindung testen
1. Starte `rojo serve`
2. Öffne Roblox Studio
3. Verbinde mit Rojo Plugin
4. Du solltest die Projektstruktur in Studio sehen

## 🐛 Troubleshooting

### Rojo nicht gefunden
- Überprüfe ob `C:\RobloxTools\rojo.exe` existiert
- Starte VS Code neu (PATH wurde aktualisiert)
- Oder verwende den vollständigen Pfad: `C:\RobloxTools\rojo.exe serve`

### VS Code Extensions nicht verfügbar
- Überprüfe Internetverbindung
- Installiere Extensions manuell über die Extension-Marketplace

### Rojo Plugin nicht in Studio
- Gehe zu: Plugins → Manage Plugins
- Suche nach "Rojo"
- Installiere das offizielle Rojo Plugin

### Port 34872 bereits belegt
```bash
# Verwende einen anderen Port
rojo serve --port 34873
```

## 📚 Nächste Schritte

Nach erfolgreicher Installation:

1. **Lies die Dokumentation**: `docs/DEVELOPMENT_GUIDE.md`
2. **Verstehe die Architektur**: `docs/ARCHITECTURE.md`
3. **Beginne mit der Entwicklung**: Erstelle dein erstes Service
4. **Teste das System**: Führe die Beispiel-Skripte aus

## 🎯 Erfolgskriterien

Du weißt, dass alles funktioniert, wenn:
- ✅ `rojo serve` startet ohne Fehler
- ✅ Roblox Studio kann sich verbinden
- ✅ Änderungen in VS Code erscheinen sofort in Studio
- ✅ Das Logger-System funktioniert (siehe Output in Studio)

## 💡 Tipps

- **Verwende immer `rojo serve`** für die Entwicklung
- **Committe regelmäßig** deine Änderungen mit Git
- **Teste auf verschiedenen Geräten** (PC & Mobile)
- **Nutze das Logger-System** für Debugging

Bei Problemen: Überprüfe die `docs/SETUP.md` für detaillierte Anweisungen!
