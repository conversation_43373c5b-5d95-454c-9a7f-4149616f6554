# GitHub Repository Setup Script
# Automatische Einrichtung des GitHub Repositories

Write-Host "GitHub Repository Setup wird gestartet..." -ForegroundColor Green

# GitHub Benutzerdaten
$githubUsername = "imknowledge"
$githubEmail = "<EMAIL>"
$repositoryName = "multifunktionales-roblox-spiel"

Write-Host "Benutzername: $githubUsername" -ForegroundColor Cyan
Write-Host "Email: $githubEmail" -ForegroundColor Cyan
Write-Host "Repository: $repositoryName" -ForegroundColor Cyan

# Git Konfiguration aktualisieren
Write-Host "Aktualisiere Git-Konfiguration..." -ForegroundColor Yellow
git config --global user.name $githubUsername
git config --global user.email $githubEmail

# Lokale Git-Konfiguration für dieses Projekt
git config user.name $githubUsername
git config user.email $githubEmail

Write-Host "Git-Konfiguration aktualisiert" -ForegroundColor Green

# Repository-URL erstellen
$repoUrl = "https://github.com/$githubUsername/$repositoryName.git"

Write-Host ""
Write-Host "MANUELLE SCHRITTE ERFORDERLICH:" -ForegroundColor Red
Write-Host "================================" -ForegroundColor Red
Write-Host ""

Write-Host "1. GITHUB REPOSITORY ERSTELLEN:" -ForegroundColor Yellow
Write-Host "   - Gehe zu: https://github.com/new" -ForegroundColor White
Write-Host "   - Repository Name: $repositoryName" -ForegroundColor White
Write-Host "   - Beschreibung: Ein komplexes Multiplayer-Roblox-Spiel mit modularer Architektur" -ForegroundColor White
Write-Host "   - Setze auf 'Public' (oder 'Private' nach Wunsch)" -ForegroundColor White
Write-Host "   - NICHT 'Initialize with README' ankreuzen (wir haben bereits Dateien)" -ForegroundColor White
Write-Host "   - Klicke 'Create repository'" -ForegroundColor White
Write-Host ""

Write-Host "2. NACH DER REPOSITORY-ERSTELLUNG:" -ForegroundColor Yellow
Write-Host "   Fuehre diese Befehle aus:" -ForegroundColor White
Write-Host ""
Write-Host "   git remote add origin $repoUrl" -ForegroundColor Cyan
Write-Host "   git branch -M main" -ForegroundColor Cyan
Write-Host "   git push -u origin main" -ForegroundColor Cyan
Write-Host ""

Write-Host "3. AUTHENTIFIZIERUNG:" -ForegroundColor Yellow
Write-Host "   Beim ersten Push wirst du nach Anmeldedaten gefragt:" -ForegroundColor White
Write-Host "   - Benutzername: $githubUsername" -ForegroundColor White
Write-Host "   - Passwort: [DEIN GITHUB PASSWORT]" -ForegroundColor White
Write-Host ""

Write-Host "ALTERNATIVE - GITHUB CLI VERWENDEN:" -ForegroundColor Yellow
Write-Host "====================================" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. GitHub CLI authentifizieren:" -ForegroundColor White
Write-Host '   "C:\Program Files\GitHub CLI\gh.exe" auth login' -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Repository erstellen:" -ForegroundColor White
Write-Host '   "C:\Program Files\GitHub CLI\gh.exe" repo create multifunktionales-roblox-spiel --public --source=. --remote=origin --push' -ForegroundColor Cyan
Write-Host ""

# Automatische Befehle vorbereiten
Write-Host "AUTOMATISCHE VORBEREITUNG:" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# README.md aktualisieren mit GitHub-spezifischen Informationen
$readmeAddition = @"

## 🔗 GitHub Repository

Dieses Projekt ist verfügbar unter: https://github.com/$githubUsername/$repositoryName

### Klonen des Repositories:
```bash
git clone https://github.com/$githubUsername/$repositoryName.git
cd $repositoryName
```

### Entwicklung beitragen:
1. Fork das Repository
2. Erstelle einen Feature-Branch: `git checkout -b feature/neue-funktion`
3. Committe deine Aenderungen: `git commit -m 'Neue Funktion hinzugefuegt'`
4. Push zum Branch: `git push origin feature/neue-funktion`
5. Erstelle einen Pull Request

"@

# README erweitern
Add-Content -Path "README.md" -Value $readmeAddition

# .gitignore für Roblox-spezifische Dateien erweitern
$gitignoreAddition = @"

# GitHub-spezifische Dateien
.github/
*.md.backup

# Roblox Studio temporäre Dateien
*.tmp
*.temp
*_temp.lua

# Lokale Konfigurationsdateien
.env.local
config.local.json

"@

Add-Content -Path ".gitignore" -Value $gitignoreAddition

# Änderungen committen
Write-Host "Committe GitHub-spezifische Änderungen..." -ForegroundColor Yellow
git add .
git commit -m "GitHub Repository Setup - README und .gitignore erweitert"

Write-Host ""
Write-Host "SETUP ABGESCHLOSSEN!" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host ""
Write-Host "Naechste Schritte:" -ForegroundColor Cyan
Write-Host "1. Erstelle das GitHub Repository manuell (siehe oben)" -ForegroundColor White
Write-Host "2. Fuehre die Git-Befehle aus, um das Projekt hochzuladen" -ForegroundColor White
Write-Host "3. Dein Projekt ist dann online verfuegbar!" -ForegroundColor White
Write-Host ""
Write-Host "Repository URL: $repoUrl" -ForegroundColor Cyan
