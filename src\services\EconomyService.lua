-- Economy Service
-- Verwaltet Währungen, Shop-System und Marktplatz

local EconomyService = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Module laden
local Config = require(ReplicatedStorage.Shared.Config)
local Logger = require(ReplicatedStorage.Shared.Logger)
local EventBus = require(ReplicatedStorage.Modules.EventBus)

-- Nur auf Server ausführen
if not RunService:IsServer() then
    return EconomyService
end

-- Private Variablen
local PlayerEconomyData = {}
local ShopItems = {}
local MarketplaceListings = {}

-- Währungstypen
local CurrencyTypes = {
    COINS = "Coins",
    GEMS = "Gems", 
    TOKENS = "Tokens",
    EXPERIENCE = "Experience"
}

-- Shop-Kategorien
local ShopCategories = {
    WEAPONS = "Weapons",
    ARMOR = "Armor",
    CONSUMABLES = "Consumables",
    MATERIALS = "Materials",
    SPECIAL = "Special"
}

-- Standard-Economy-Daten erstellen
local function createDefaultEconomyData()
    return {
        Currencies = {
            [CurrencyTypes.COINS] = Config.Economy.StartingAmount,
            [CurrencyTypes.GEMS] = 0,
            [CurrencyTypes.TOKENS] = 0,
            [CurrencyTypes.EXPERIENCE] = 0
        },
        TransactionHistory = {},
        ShopPurchases = {},
        MarketplaceSales = {},
        DailyBonusLastClaimed = 0
    }
end

-- Shop-Items initialisieren
local function initializeShop()
    ShopItems = {
        [ShopCategories.WEAPONS] = {
            {
                ItemId = "sword_iron",
                Price = {[CurrencyTypes.COINS] = 250},
                Stock = -1, -- Unbegrenzt
                RequiredLevel = 1,
                Featured = false
            },
            {
                ItemId = "sword_steel",
                Price = {[CurrencyTypes.COINS] = 500, [CurrencyTypes.GEMS] = 10},
                Stock = -1,
                RequiredLevel = 5,
                Featured = true
            }
        },
        [ShopCategories.ARMOR] = {
            {
                ItemId = "armor_leather",
                Price = {[CurrencyTypes.COINS] = 150},
                Stock = -1,
                RequiredLevel = 1,
                Featured = false
            },
            {
                ItemId = "armor_iron",
                Price = {[CurrencyTypes.COINS] = 400},
                Stock = -1,
                RequiredLevel = 3,
                Featured = false
            }
        },
        [ShopCategories.CONSUMABLES] = {
            {
                ItemId = "potion_health",
                Price = {[CurrencyTypes.COINS] = 25},
                Stock = -1,
                RequiredLevel = 1,
                Featured = false
            },
            {
                ItemId = "potion_mana",
                Price = {[CurrencyTypes.COINS] = 30},
                Stock = -1,
                RequiredLevel = 1,
                Featured = false
            }
        },
        [ShopCategories.MATERIALS] = {
            {
                ItemId = "iron_ore",
                Price = {[CurrencyTypes.COINS] = 15},
                Stock = -1,
                RequiredLevel = 1,
                Featured = false
            },
            {
                ItemId = "gold_ore",
                Price = {[CurrencyTypes.COINS] = 50, [CurrencyTypes.TOKENS] = 1},
                Stock = 100,
                RequiredLevel = 10,
                Featured = true
            }
        },
        [ShopCategories.SPECIAL] = {
            {
                ItemId = "daily_bonus_multiplier",
                Price = {[CurrencyTypes.GEMS] = 50},
                Stock = 1,
                RequiredLevel = 1,
                Featured = true
            }
        }
    }
    
    Logger.systemEvent("EconomyService", "ShopInitialized", 
        string.format("%d Kategorien mit Items geladen", #ShopItems))
end

-- Spieler-Economy-Daten laden
function EconomyService.LoadPlayerEconomy(player)
    if PlayerEconomyData[player.UserId] then
        return PlayerEconomyData[player.UserId]
    end
    
    PlayerEconomyData[player.UserId] = createDefaultEconomyData()
    Logger.playerAction(player, "EconomyLoaded", "Neue Economy-Daten erstellt")
    return PlayerEconomyData[player.UserId]
end

-- Währung hinzufügen
function EconomyService.AddCurrency(player, currencyType, amount, reason)
    local economyData = PlayerEconomyData[player.UserId]
    if not economyData or not economyData.Currencies[currencyType] then
        Logger.warn("AddCurrency fehlgeschlagen: Ungültige Daten")
        return false
    end
    
    economyData.Currencies[currencyType] = economyData.Currencies[currencyType] + amount
    
    -- Transaktion protokollieren
    table.insert(economyData.TransactionHistory, {
        Type = "ADD",
        Currency = currencyType,
        Amount = amount,
        Reason = reason or "Unknown",
        Timestamp = os.time()
    })
    
    Logger.playerAction(player, "CurrencyAdded", 
        string.format("%s +%d (%s)", currencyType, amount, reason or ""))
    
    EventBus:FireClient("CurrencyUpdated", player, economyData.Currencies)
    return true
end

-- Währung entfernen
function EconomyService.RemoveCurrency(player, currencyType, amount, reason)
    local economyData = PlayerEconomyData[player.UserId]
    if not economyData or not economyData.Currencies[currencyType] then
        return false
    end
    
    if economyData.Currencies[currencyType] < amount then
        Logger.warn("RemoveCurrency fehlgeschlagen: Nicht genug %s", currencyType)
        return false
    end
    
    economyData.Currencies[currencyType] = economyData.Currencies[currencyType] - amount
    
    -- Transaktion protokollieren
    table.insert(economyData.TransactionHistory, {
        Type = "REMOVE",
        Currency = currencyType,
        Amount = amount,
        Reason = reason or "Unknown",
        Timestamp = os.time()
    })
    
    Logger.playerAction(player, "CurrencyRemoved", 
        string.format("%s -%d (%s)", currencyType, amount, reason or ""))
    
    EventBus:FireClient("CurrencyUpdated", player, economyData.Currencies)
    return true
end

-- Item kaufen
function EconomyService.PurchaseItem(player, category, itemIndex, quantity)
    quantity = quantity or 1
    local economyData = PlayerEconomyData[player.UserId]
    
    if not economyData or not ShopItems[category] or not ShopItems[category][itemIndex] then
        Logger.warn("PurchaseItem fehlgeschlagen: Ungültige Parameter")
        return false
    end
    
    local shopItem = ShopItems[category][itemIndex]
    
    -- Prüfe Stock
    if shopItem.Stock > 0 and shopItem.Stock < quantity then
        Logger.warn("PurchaseItem fehlgeschlagen: Nicht genug auf Lager")
        EventBus:FireClient("PurchaseResult", player, false, "Nicht genug auf Lager")
        return false
    end
    
    -- Prüfe Level-Anforderung
    local playerLevel = economyData.Currencies[CurrencyTypes.EXPERIENCE] / 100 -- Vereinfacht
    if playerLevel < shopItem.RequiredLevel then
        Logger.warn("PurchaseItem fehlgeschlagen: Level zu niedrig")
        EventBus:FireClient("PurchaseResult", player, false, "Level zu niedrig")
        return false
    end
    
    -- Prüfe und entferne Währung
    local totalCost = {}
    for currencyType, cost in pairs(shopItem.Price) do
        totalCost[currencyType] = cost * quantity
        if economyData.Currencies[currencyType] < totalCost[currencyType] then
            Logger.warn("PurchaseItem fehlgeschlagen: Nicht genug %s", currencyType)
            EventBus:FireClient("PurchaseResult", player, false, "Nicht genug " .. currencyType)
            return false
        end
    end
    
    -- Währung abziehen
    for currencyType, cost in pairs(totalCost) do
        EconomyService.RemoveCurrency(player, currencyType, cost, "Shop Purchase")
    end
    
    -- Stock reduzieren
    if shopItem.Stock > 0 then
        shopItem.Stock = shopItem.Stock - quantity
    end
    
    -- Item zum Inventar hinzufügen (über InventoryService)
    local InventoryService = require(script.Parent.InventoryService)
    local success = InventoryService.AddItem(player, shopItem.ItemId, quantity)
    
    if success then
        -- Kauf protokollieren
        table.insert(economyData.ShopPurchases, {
            ItemId = shopItem.ItemId,
            Quantity = quantity,
            Cost = totalCost,
            Timestamp = os.time()
        })
        
        Logger.playerAction(player, "ItemPurchased", 
            string.format("%s x%d gekauft", shopItem.ItemId, quantity))
        
        EventBus:FireClient("PurchaseResult", player, true, "Kauf erfolgreich")
        return true
    else
        -- Währung zurückgeben bei Inventar-Fehler
        for currencyType, cost in pairs(totalCost) do
            EconomyService.AddCurrency(player, currencyType, cost, "Purchase Refund")
        end
        
        EventBus:FireClient("PurchaseResult", player, false, "Inventar voll")
        return false
    end
end

-- Täglichen Bonus beanspruchen
function EconomyService.ClaimDailyBonus(player)
    local economyData = PlayerEconomyData[player.UserId]
    if not economyData then
        return false
    end
    
    local currentDay = math.floor(os.time() / 86400) -- Tage seit Epoch
    local lastClaimedDay = math.floor(economyData.DailyBonusLastClaimed / 86400)
    
    if currentDay <= lastClaimedDay then
        Logger.warn("DailyBonus bereits heute beansprucht")
        EventBus:FireClient("DailyBonusResult", player, false, "Bereits heute beansprucht")
        return false
    end
    
    -- Bonus berechnen (aufeinanderfolgend = mehr Bonus)
    local streak = currentDay - lastClaimedDay == 1 and 
        (economyData.DailyBonusStreak or 0) + 1 or 1
    
    local bonusAmount = Config.Economy.DailyBonus * streak
    economyData.DailyBonusStreak = streak
    economyData.DailyBonusLastClaimed = os.time()
    
    EconomyService.AddCurrency(player, CurrencyTypes.COINS, bonusAmount, "Daily Bonus")
    
    Logger.playerAction(player, "DailyBonusClaimed", 
        string.format("Tag %d: %d Coins", streak, bonusAmount))
    
    EventBus:FireClient("DailyBonusResult", player, true, 
        string.format("Tag %d: %d Coins erhalten!", streak, bonusAmount))
    
    return true
end

-- Shop-Daten abrufen
function EconomyService.GetShopData(category)
    return category and ShopItems[category] or ShopItems
end

-- Economy-Daten abrufen
function EconomyService.GetPlayerEconomy(player)
    return PlayerEconomyData[player.UserId]
end

-- Service initialisieren
function EconomyService.Initialize()
    initializeShop()
    
    -- Event-Handler
    EventBus:OnServerEvent("GetShopData", function(player, category)
        local shopData = EconomyService.GetShopData(category)
        EventBus:FireClient("ShopData", player, shopData)
    end)
    
    EventBus:OnServerEvent("PurchaseItem", function(player, category, itemIndex, quantity)
        EconomyService.PurchaseItem(player, category, itemIndex, quantity)
    end)
    
    EventBus:OnServerEvent("ClaimDailyBonus", function(player)
        EconomyService.ClaimDailyBonus(player)
    end)
    
    EventBus:OnServerEvent("GetEconomyData", function(player)
        local economyData = EconomyService.GetPlayerEconomy(player)
        EventBus:FireClient("EconomyData", player, economyData)
    end)
    
    Logger.systemEvent("EconomyService", "Initialized", "Service bereit")
end

-- Cleanup beim Verlassen
function EconomyService.CleanupPlayer(player)
    PlayerEconomyData[player.UserId] = nil
    Logger.playerAction(player, "EconomyCleanup", "Economy-Daten aus Speicher entfernt")
end

return EconomyService
