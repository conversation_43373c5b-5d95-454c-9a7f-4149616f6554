-- Components Module
-- Enthält alle UI-Komponenten

local Components = {}

-- Services
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Module
local EventBus = require(ReplicatedStorage.Modules.EventBus)
local Logger = require(ReplicatedStorage.Shared.Logger)

-- Lokale Variablen
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Inventory UI erstellen
function Components.CreateInventoryUI()
    -- Hauptframe
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "InventoryUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui

    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "InventoryFrame"
    mainFrame.Size = UDim2.new(0, 600, 0, 400)
    mainFrame.Position = UDim2.new(0.5, -300, 0.5, -200)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui

    -- Titel
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "Title"
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "🎒 Inventar"
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame

    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame

    -- Items-Container
    local itemsFrame = Instance.new("ScrollingFrame")
    itemsFrame.Name = "ItemsFrame"
    itemsFrame.Size = UDim2.new(0.7, -10, 1, -50)
    itemsFrame.Position = UDim2.new(0, 5, 0, 45)
    itemsFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    itemsFrame.BorderSizePixel = 0
    itemsFrame.ScrollBarThickness = 8
    itemsFrame.Parent = mainFrame

    -- Equipment-Frame
    local equipmentFrame = Instance.new("Frame")
    equipmentFrame.Name = "EquipmentFrame"
    equipmentFrame.Size = UDim2.new(0.3, -10, 1, -50)
    equipmentFrame.Position = UDim2.new(0.7, 5, 0, 45)
    equipmentFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    equipmentFrame.BorderSizePixel = 0
    equipmentFrame.Parent = mainFrame

    -- Equipment-Titel
    local equipTitle = Instance.new("TextLabel")
    equipTitle.Name = "EquipTitle"
    equipTitle.Size = UDim2.new(1, 0, 0, 30)
    equipTitle.Position = UDim2.new(0, 0, 0, 0)
    equipTitle.BackgroundTransparency = 1
    equipTitle.Text = "⚔️ Ausrüstung"
    equipTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    equipTitle.TextScaled = true
    equipTitle.Font = Enum.Font.SourceSansBold
    equipTitle.Parent = equipmentFrame

    return screenGui
end

-- Inventory UI Toggle
function Components.ToggleInventory()
    local inventoryUI = playerGui:FindFirstChild("InventoryUI")
    if not inventoryUI then
        inventoryUI = Components.CreateInventoryUI()
    end

    local mainFrame = inventoryUI.InventoryFrame
    mainFrame.Visible = not mainFrame.Visible

    if mainFrame.Visible then
        -- Inventar-Daten anfordern
        EventBus:FireServer("GetInventory")
        Logger.info("Inventar geöffnet")
    else
        Logger.info("Inventar geschlossen")
    end
end

-- Input-Handler
function Components.SetupInputHandlers()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        if input.KeyCode == Enum.KeyCode.I then
            Components.ToggleInventory()
        elseif input.KeyCode == Enum.KeyCode.P then
            Components.ToggleShop()
        end
    end)

    Logger.info("Input-Handler eingerichtet (I = Inventar, P = Shop)")
end

-- Event-Handler für Inventar-Updates
function Components.SetupInventoryEvents()
    EventBus:OnClientEvent("InventoryData", function(inventoryData)
        Components.UpdateInventoryDisplay(inventoryData)
    end)

    EventBus:OnClientEvent("InventoryUpdated", function(inventoryData)
        Components.UpdateInventoryDisplay(inventoryData)
    end)

    Logger.info("Inventar Event-Handler eingerichtet")
end

-- Inventar-Anzeige aktualisieren
function Components.UpdateInventoryDisplay(inventoryData)
    local inventoryUI = playerGui:FindFirstChild("InventoryUI")
    if not inventoryUI then return end

    local itemsFrame = inventoryUI.InventoryFrame.ItemsFrame

    -- Lösche alte Items
    for _, child in ipairs(itemsFrame:GetChildren()) do
        if child:IsA("Frame") then
            child:Destroy()
        end
    end

    -- Erstelle Item-Slots
    local slotSize = 60
    local padding = 5
    local slotsPerRow = math.floor((itemsFrame.AbsoluteSize.X - padding) / (slotSize + padding))

    for i, item in ipairs(inventoryData.Items) do
        local row = math.floor((i - 1) / slotsPerRow)
        local col = (i - 1) % slotsPerRow

        local itemSlot = Instance.new("Frame")
        itemSlot.Name = "ItemSlot_" .. i
        itemSlot.Size = UDim2.new(0, slotSize, 0, slotSize)
        itemSlot.Position = UDim2.new(0, col * (slotSize + padding) + padding, 0, row * (slotSize + padding) + padding)
        itemSlot.BackgroundColor3 = item.Data.Rarity.Color
        itemSlot.BorderSizePixel = 1
        itemSlot.BorderColor3 = Color3.fromRGB(100, 100, 100)
        itemSlot.Parent = itemsFrame

        -- Item-Name
        local itemLabel = Instance.new("TextLabel")
        itemLabel.Size = UDim2.new(1, 0, 0.7, 0)
        itemLabel.Position = UDim2.new(0, 0, 0, 0)
        itemLabel.BackgroundTransparency = 1
        itemLabel.Text = item.Data.Name
        itemLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        itemLabel.TextScaled = true
        itemLabel.Font = Enum.Font.SourceSans
        itemLabel.Parent = itemSlot

        -- Anzahl
        if item.Quantity > 1 then
            local quantityLabel = Instance.new("TextLabel")
            quantityLabel.Size = UDim2.new(1, 0, 0.3, 0)
            quantityLabel.Position = UDim2.new(0, 0, 0.7, 0)
            quantityLabel.BackgroundTransparency = 1
            quantityLabel.Text = "x" .. item.Quantity
            quantityLabel.TextColor3 = Color3.fromRGB(255, 255, 0)
            quantityLabel.TextScaled = true
            quantityLabel.Font = Enum.Font.SourceSansBold
            quantityLabel.Parent = itemSlot
        end
    end

    -- Canvas-Größe anpassen
    local totalRows = math.ceil(#inventoryData.Items / slotsPerRow)
    itemsFrame.CanvasSize = UDim2.new(0, 0, 0, totalRows * (slotSize + padding) + padding)

    Logger.info("Inventar-Anzeige aktualisiert: %d Items", #inventoryData.Items)
end

-- Shop UI erstellen
function Components.CreateShopUI()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "ShopUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui

    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "ShopFrame"
    mainFrame.Size = UDim2.new(0, 800, 0, 500)
    mainFrame.Position = UDim2.new(0.5, -400, 0.5, -250)
    mainFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui

    -- Titel
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "Title"
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.fromRGB(25, 25, 25)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "🛒 Shop"
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame

    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame

    closeButton.MouseButton1Click:Connect(function()
        Components.ToggleShop()
    end)

    -- Währungs-Anzeige
    local currencyFrame = Instance.new("Frame")
    currencyFrame.Name = "CurrencyFrame"
    currencyFrame.Size = UDim2.new(1, 0, 0, 30)
    currencyFrame.Position = UDim2.new(0, 0, 0, 45)
    currencyFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 45)
    currencyFrame.BorderSizePixel = 0
    currencyFrame.Parent = mainFrame

    -- Kategorien-Frame
    local categoriesFrame = Instance.new("Frame")
    categoriesFrame.Name = "CategoriesFrame"
    categoriesFrame.Size = UDim2.new(0, 150, 1, -80)
    categoriesFrame.Position = UDim2.new(0, 5, 0, 80)
    categoriesFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    categoriesFrame.BorderSizePixel = 0
    categoriesFrame.Parent = mainFrame

    -- Items-Frame
    local itemsFrame = Instance.new("ScrollingFrame")
    itemsFrame.Name = "ItemsFrame"
    itemsFrame.Size = UDim2.new(1, -165, 1, -80)
    itemsFrame.Position = UDim2.new(0, 160, 0, 80)
    itemsFrame.BackgroundColor3 = Color3.fromRGB(55, 55, 55)
    itemsFrame.BorderSizePixel = 0
    itemsFrame.ScrollBarThickness = 8
    itemsFrame.Parent = mainFrame

    return screenGui
end

-- Shop UI Toggle
function Components.ToggleShop()
    local shopUI = playerGui:FindFirstChild("ShopUI")
    if not shopUI then
        shopUI = Components.CreateShopUI()
    end

    local mainFrame = shopUI.ShopFrame
    mainFrame.Visible = not mainFrame.Visible

    if mainFrame.Visible then
        EventBus:FireServer("GetShopData")
        EventBus:FireServer("GetEconomyData")
        Logger.info("Shop geöffnet")
    else
        Logger.info("Shop geschlossen")
    end
end

-- Event-Handler für Shop-Updates
function Components.SetupShopEvents()
    EventBus:OnClientEvent("ShopData", function(shopData)
        Components.UpdateShopDisplay(shopData)
    end)

    EventBus:OnClientEvent("EconomyData", function(economyData)
        Components.UpdateCurrencyDisplay(economyData.Currencies)
    end)

    EventBus:OnClientEvent("CurrencyUpdated", function(currencies)
        Components.UpdateCurrencyDisplay(currencies)
    end)

    EventBus:OnClientEvent("PurchaseResult", function(success, message)
        Components.ShowPurchaseResult(success, message)
    end)

    Logger.info("Shop Event-Handler eingerichtet")
end

-- Währungs-Anzeige aktualisieren
function Components.UpdateCurrencyDisplay(currencies)
    local shopUI = playerGui:FindFirstChild("ShopUI")
    if not shopUI then return end

    local currencyFrame = shopUI.ShopFrame.CurrencyFrame

    -- Lösche alte Anzeigen
    for _, child in ipairs(currencyFrame:GetChildren()) do
        if child:IsA("TextLabel") then
            child:Destroy()
        end
    end

    -- Erstelle Währungs-Labels
    local currencyTypes = {"Coins", "Gems", "Tokens"}
    for i, currencyType in ipairs(currencyTypes) do
        local label = Instance.new("TextLabel")
        label.Name = currencyType .. "Label"
        label.Size = UDim2.new(0, 100, 1, 0)
        label.Position = UDim2.new(0, (i-1) * 105 + 10, 0, 0)
        label.BackgroundTransparency = 1
        label.Text = currencyType .. ": " .. (currencies[currencyType] or 0)
        label.TextColor3 = Color3.fromRGB(255, 255, 255)
        label.TextScaled = true
        label.Font = Enum.Font.SourceSansBold
        label.Parent = currencyFrame
    end
end

-- Komponenten initialisieren
function Components.Initialize()
    Components.SetupInputHandlers()
    Components.SetupInventoryEvents()
    Components.SetupShopEvents()
    Logger.systemEvent("Components", "Initialized", "UI-Komponenten bereit")
end

return Components
