{"name": "multifunktionales-roblox-spiel", "version": "1.0.0", "description": "Ein komplexes Multiplayer-Roblox-Spiel mit modularer Architektur", "scripts": {"serve": "rojo serve", "build": "rojo build -o game.rbxl", "test": "echo \"Tests werden später implementiert\" && exit 1", "lint": "echo \"Linting wird später implementiert\" && exit 1"}, "keywords": ["roblo<PERSON>", "game", "multiplayer", "lua"], "author": "IL-pc", "license": "MIT", "devDependencies": {"@roblox/rojo": "^7.0.0"}, "repository": {"type": "git", "url": "https://github.com/DEIN_USERNAME/multifunktionales-roblox-spiel.git"}}