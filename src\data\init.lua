-- Data Module
-- Enthält alle Spieldaten-Definitionen

local Data = {}

-- Item-Kategorien
Data.ItemTypes = {
    WEAPON = "Weapon",
    ARMOR = "Armor",
    CONSUMABLE = "Consumable",
    MATERIAL = "Material",
    QUEST = "Quest",
    CURRENCY = "Currency"
}

-- Item-Seltenheiten
Data.ItemRarity = {
    COMMON = {Name = "Common", Color = Color3.fromRGB(255, 255, 255), Multiplier = 1},
    UNCOMMON = {Name = "Uncommon", Color = Color3.fromRGB(0, 255, 0), Multiplier = 1.2},
    RARE = {Name = "Rare", Color = Color3.fromRGB(0, 100, 255), Multiplier = 1.5},
    EPIC = {Name = "Epic", Color = Color3.fromRGB(150, 0, 255), Multiplier = 2},
    LEGENDARY = {Name = "Legendary", Color = Color3.fromRGB(255, 165, 0), Multiplier = 3}
}

-- <PERSON><PERSON><PERSON><PERSON><PERSON> (für später)
Data.GameModes = {
    SURVIVAL = {Name = "Survival", MaxPlayers = 12, Description = "Überlebe so lange wie möglich"},
    BATTLE = {Name = "Battle", MaxPlayers = 8, Description = "Kämpfe gegen andere Spieler"},
    CREATIVE = {Name = "Creative", MaxPlayers = 16, Description = "Baue und erschaffe frei"},
    ADVENTURE = {Name = "Adventure", MaxPlayers = 4, Description = "Erkunde die Welt mit Freunden"}
}

-- Quest-Typen (für später)
Data.QuestTypes = {
    KILL = "Kill",
    COLLECT = "Collect",
    CRAFT = "Craft",
    EXPLORE = "Explore",
    DELIVER = "Deliver"
}

return Data
