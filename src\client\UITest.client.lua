-- UI Test Client - Einfacher Test für Input-Handler
-- Testet ob die Tasteneingaben funktionieren

print("🧪 UI Test Client wird gestartet...")

-- Services
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Lokale Variablen
local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Einfache Test-UI erstellen
local function createTestUI()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "TestUI"
    screenGui.Parent = playerGui
    
    local frame = Instance.new("Frame")
    frame.Name = "TestFrame"
    frame.Size = UDim2.new(0, 300, 0, 100)
    frame.Position = UDim2.new(0, 10, 0, 10)
    frame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    frame.BackgroundTransparency = 0.3
    frame.BorderSizePixel = 0
    frame.Parent = screenGui
    
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🎮 Drücke I für Inventar, P für Shop"
    label.TextColor3 = Color3.fromRGB(255, 255, 255)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSansBold
    label.Parent = frame
    
    return screenGui
end

-- Test-Input-Handler
local function setupTestInputHandler()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.I then
            print("✅ I-Taste gedrückt - Inventar sollte sich öffnen")
            
            -- Einfaches Test-Inventar erstellen
            local existingInventory = playerGui:FindFirstChild("SimpleInventory")
            if existingInventory then
                existingInventory:Destroy()
                print("📦 Inventar geschlossen")
            else
                local inventoryGui = Instance.new("ScreenGui")
                inventoryGui.Name = "SimpleInventory"
                inventoryGui.Parent = playerGui
                
                local inventoryFrame = Instance.new("Frame")
                inventoryFrame.Size = UDim2.new(0, 400, 0, 300)
                inventoryFrame.Position = UDim2.new(0.5, -200, 0.5, -150)
                inventoryFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
                inventoryFrame.BorderSizePixel = 2
                inventoryFrame.BorderColor3 = Color3.fromRGB(100, 100, 100)
                inventoryFrame.Parent = inventoryGui
                
                local title = Instance.new("TextLabel")
                title.Size = UDim2.new(1, 0, 0, 40)
                title.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
                title.BorderSizePixel = 0
                title.Text = "🎒 Test-Inventar"
                title.TextColor3 = Color3.fromRGB(255, 255, 255)
                title.TextScaled = true
                title.Font = Enum.Font.SourceSansBold
                title.Parent = inventoryFrame
                
                local closeButton = Instance.new("TextButton")
                closeButton.Size = UDim2.new(0, 30, 0, 30)
                closeButton.Position = UDim2.new(1, -35, 0, 5)
                closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
                closeButton.BorderSizePixel = 0
                closeButton.Text = "X"
                closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
                closeButton.TextScaled = true
                closeButton.Font = Enum.Font.SourceSansBold
                closeButton.Parent = inventoryFrame
                
                closeButton.MouseButton1Click:Connect(function()
                    inventoryGui:Destroy()
                    print("📦 Inventar geschlossen")
                end)
                
                print("📦 Test-Inventar geöffnet")
            end
            
        elseif input.KeyCode == Enum.KeyCode.P then
            print("✅ P-Taste gedrückt - Shop sollte sich öffnen")
            
            -- Einfaches Test-Shop erstellen
            local existingShop = playerGui:FindFirstChild("SimpleShop")
            if existingShop then
                existingShop:Destroy()
                print("🛒 Shop geschlossen")
            else
                local shopGui = Instance.new("ScreenGui")
                shopGui.Name = "SimpleShop"
                shopGui.Parent = playerGui
                
                local shopFrame = Instance.new("Frame")
                shopFrame.Size = UDim2.new(0, 500, 0, 400)
                shopFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
                shopFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
                shopFrame.BorderSizePixel = 2
                shopFrame.BorderColor3 = Color3.fromRGB(100, 100, 100)
                shopFrame.Parent = shopGui
                
                local title = Instance.new("TextLabel")
                title.Size = UDim2.new(1, 0, 0, 40)
                title.BackgroundColor3 = Color3.fromRGB(20, 20, 20)
                title.BorderSizePixel = 0
                title.Text = "🛒 Test-Shop"
                title.TextColor3 = Color3.fromRGB(255, 255, 255)
                title.TextScaled = true
                title.Font = Enum.Font.SourceSansBold
                title.Parent = shopFrame
                
                local closeButton = Instance.new("TextButton")
                closeButton.Size = UDim2.new(0, 30, 0, 30)
                closeButton.Position = UDim2.new(1, -35, 0, 5)
                closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
                closeButton.BorderSizePixel = 0
                closeButton.Text = "X"
                closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
                closeButton.TextScaled = true
                closeButton.Font = Enum.Font.SourceSansBold
                closeButton.Parent = shopFrame
                
                closeButton.MouseButton1Click:Connect(function()
                    shopGui:Destroy()
                    print("🛒 Shop geschlossen")
                end)
                
                -- Test-Items hinzufügen
                local itemsLabel = Instance.new("TextLabel")
                itemsLabel.Size = UDim2.new(1, -20, 1, -60)
                itemsLabel.Position = UDim2.new(0, 10, 0, 50)
                itemsLabel.BackgroundTransparency = 1
                itemsLabel.Text = "🗡️ Schwert - 100 Coins\n🛡️ Rüstung - 150 Coins\n🧪 Trank - 25 Coins\n\n(Test-Shop - Käufe noch nicht funktional)"
                itemsLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
                itemsLabel.TextScaled = true
                itemsLabel.Font = Enum.Font.SourceSans
                itemsLabel.TextXAlignment = Enum.TextXAlignment.Left
                itemsLabel.TextYAlignment = Enum.TextYAlignment.Top
                itemsLabel.Parent = shopFrame
                
                print("🛒 Test-Shop geöffnet")
            end
        end
    end)
    
    print("⌨️ Input-Handler eingerichtet")
end

-- Initialisierung
local function initialize()
    print("🎮 UI Test wird initialisiert...")
    
    -- Test-UI erstellen
    createTestUI()
    
    -- Input-Handler einrichten
    setupTestInputHandler()
    
    print("✅ UI Test bereit! Drücke I oder P zum Testen")
end

-- Nach kurzer Verzögerung initialisieren
wait(1)
initialize()
