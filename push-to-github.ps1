# GitHub Push Script mit automatischer Authentifizierung

Write-Host "Pushe Projekt zu GitHub..." -ForegroundColor Green

# Versuche GitHub CLI Authentifizierung
Write-Host "Versuche GitHub CLI Authentifizierung..." -ForegroundColor Yellow

try {
    # GitHub CLI Login mit Browser
    & "C:\Program Files\GitHub CLI\gh.exe" auth login --web
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "GitHub CLI erfolgreich authentifiziert" -ForegroundColor Green
        
        # Push mit GitHub CLI
        Write-Host "Pushe mit GitHub CLI..." -ForegroundColor Yellow
        git push -u origin main
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Projekt erfolgreich zu GitHub gepusht!" -ForegroundColor Green
            Write-Host "Repository URL: https://github.com/imknowledge/multifunktionales-roblox-spiel" -ForegroundColor Cyan
        } else {
            Write-Host "Push fehlgeschlagen" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "GitHub CLI Authentifizierung fehlgeschlagen" -ForegroundColor Red
    Write-Host "Manuelle Schritte erforderlich:" -ForegroundColor Yellow
    Write-Host "1. Gehe zu: https://github.com/settings/tokens" -ForegroundColor White
    Write-Host "2. Erstelle einen neuen Personal Access Token" -ForegroundColor White
    Write-Host "3. Verwende den Token als Passwort beim git push" -ForegroundColor White
    Write-Host "4. Fuehre aus: git push -u origin main" -ForegroundColor White
}

Write-Host ""
Write-Host "ALTERNATIVE - Manueller Push:" -ForegroundColor Yellow
Write-Host "git push -u origin main" -ForegroundColor Cyan
Write-Host ""
Write-Host "Bei Passwort-Abfrage:" -ForegroundColor Yellow
Write-Host "- Benutzername: imknowledge" -ForegroundColor White
Write-Host "- Passwort: [Personal Access Token von GitHub]" -ForegroundColor White
