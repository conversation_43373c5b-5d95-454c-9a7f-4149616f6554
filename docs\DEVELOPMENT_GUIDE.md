# 📚 Entwicklungshandbuch

## Erste Schritte

### 1. Setup abschließen
Folge der [SETUP.md](SETUP.md) Anleitung um deine Entwicklungsumgebung einzurichten.

### 2. Projekt starten
```bash
# Terminal öffnen in VS Code (Strg + `)
cd c:\Users\<USER>\RobloxEntwicklung

# Rojo starten
rojo serve

# In einem neuen Terminal (optional)
npm run serve
```

### 3. Mit Roblox Studio verbinden
1. Öffne Roblox Studio
2. Installiere das Rojo Plugin (falls noch nicht geschehen)
3. <PERSON><PERSON><PERSON> "Connect" im Rojo Plugin
4. <PERSON><PERSON><PERSON>e "localhost:34872"

## Entwicklungsworkflow

### Täglicher Workflow
1. **VS Code öffnen** mit dem Projekt
2. **Rojo starten**: `rojo serve`
3. **Roblox Studio öffnen** und verbinden
4. **Code schreiben** in VS Code
5. **Testen** in Roblox Studio
6. **Änderungen committen**

### Code-Konventionen

#### Dateinamen
- PascalCase für Module: `PlayerDataService.lua`
- camelCase für Utilities: `mathUtils.lua`
- Ordner in lowercase: `services/`, `modules/`

#### Lua-Stil
```lua
-- Variablen: camelCase
local playerData = {}
local maxHealth = 100

-- Funktionen: camelCase
local function calculateDamage(baseDamage, multiplier)
    return baseDamage * multiplier
end

-- Konstanten: UPPER_CASE
local MAX_PLAYERS = 12
local DEFAULT_CURRENCY = 100

-- Module: PascalCase
local PlayerDataService = {}

function PlayerDataService.LoadData(player)
    -- Implementation
end

return PlayerDataService
```

#### Kommentare
```lua
-- Einzeilige Kommentare für kurze Erklärungen

--[[
    Mehrzeilige Kommentare für:
    - Komplexe Algorithmen
    - API-Dokumentation
    - Wichtige Hinweise
]]

--- JSDoc-Stil für Funktionen
--- @param player Player Der Spieler
--- @param amount number Die Menge
--- @return boolean Erfolg
function addCurrency(player, amount)
    -- Implementation
end
```

## Architektur verstehen

### Service-Pattern
```lua
-- Jeder Service folgt diesem Muster:
local ServiceName = {}

-- Private Variablen
local privateData = {}

-- Öffentliche Funktionen
function ServiceName.PublicFunction(parameters)
    -- Implementation
end

-- Private Funktionen
local function privateFunction()
    -- Implementation
end

-- Initialisierung
function ServiceName.Initialize()
    -- Setup-Code
end

return ServiceName
```

### Event-System nutzen
```lua
local EventBus = require(ReplicatedStorage.Modules.EventBus)

-- Event senden
EventBus:Fire("PlayerLevelUp", player, newLevel)

-- Event empfangen
EventBus:Connect("PlayerLevelUp", function(player, level)
    print(player.Name .. " ist jetzt Level " .. level)
end)

-- Remote Events (Client ↔ Server)
-- Server → Client
EventBus:FireClient("UpdateUI", player, data)

-- Client → Server
EventBus:FireServer("BuyItem", itemId)
```

## Neue Features entwickeln

### 1. Service erstellen
```lua
-- src/services/NewService.lua
local NewService = {}

-- Dependencies
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Config = require(ReplicatedStorage.Shared.Config)
local Logger = require(ReplicatedStorage.Shared.Logger)
local EventBus = require(ReplicatedStorage.Modules.EventBus)

-- Implementation
function NewService.DoSomething()
    Logger.info("NewService: Etwas wurde getan")
end

return NewService
```

### 2. Service registrieren
```lua
-- In src/server/init.server.lua oder src/client/init.client.lua
local NewService = require(script.Parent.Services.NewService)

-- Service initialisieren
NewService.Initialize()
```

### 3. Tests schreiben (später)
```lua
-- tests/NewService.test.lua
local NewService = require(src.services.NewService)

-- Test-Cases hier
```

## Debugging

### Logger verwenden
```lua
local Logger = require(ReplicatedStorage.Shared.Logger)

-- Verschiedene Log-Level
Logger.debug("Debug-Information")
Logger.info("Allgemeine Information")
Logger.warn("Warnung")
Logger.error("Fehler")

-- Spezielle Logger
Logger.playerAction(player, "ItemBought", "Sword")
Logger.systemEvent("Economy", "ShopOpened", "MainShop")
```

### Roblox Studio Debugging
1. **Output-Fenster** für Logs
2. **Command Bar** für schnelle Tests
3. **Explorer** für Objekthierarchie
4. **Properties** für Objekteigenschaften

## Häufige Aufgaben

### Neuen Spielmodus hinzufügen
1. Definition in `src/data/GameModes.lua`
2. Logic in entsprechendem Service
3. UI in `src/components/`
4. Tests schreiben

### Neues Item hinzufügen
1. Definition in `src/data/Items.lua`
2. Logic in `InventoryService`
3. UI-Updates in Inventory-Component

### Neue UI erstellen
1. Component in `src/components/`
2. Event-Handler definieren
3. Mit Services verbinden

## Performance-Tipps

### Lua-Optimierungen
```lua
-- Lokale Referenzen für häufig verwendete Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Tabellen-Pooling für häufige Operationen
local tablePool = {}

local function getTable()
    return table.remove(tablePool) or {}
end

local function returnTable(t)
    table.clear(t)
    table.insert(tablePool, t)
end
```

### Event-Optimierungen
```lua
-- Debouncing für häufige Events
local lastUpdate = 0
local UPDATE_INTERVAL = 0.1

local function onUpdate()
    local now = tick()
    if now - lastUpdate < UPDATE_INTERVAL then
        return
    end
    lastUpdate = now
    
    -- Update-Logic
end
```

## Troubleshooting

### Häufige Probleme

1. **Rojo verbindet nicht**
   - Port 34872 prüfen
   - Firewall-Einstellungen
   - Rojo neu starten

2. **Module nicht gefunden**
   - Pfade in default.project.json prüfen
   - require()-Pfade überprüfen

3. **Events funktionieren nicht**
   - EventBus korrekt initialisiert?
   - RemoteEvents erstellt?
   - Client/Server-Seite beachten

### Debug-Schritte
1. **Logs prüfen** (Output-Fenster)
2. **Objekthierarchie prüfen** (Explorer)
3. **Netzwerk-Events prüfen** (F9 → Network)
4. **Performance prüfen** (F9 → Performance)
